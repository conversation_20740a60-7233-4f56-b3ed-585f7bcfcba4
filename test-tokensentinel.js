/**
 * TokenSentinel Integration Test Runner
 * Run with: node test-tokensentinel.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_TOKENS = {
  SAFE_TOKEN: 'So11111111111111111111111111111111111111112', // Wrapped SOL
  RISKY_TOKEN: '7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr', // Example token
  INVALID_TOKEN: 'invalid_address_for_testing',
};

async function runTest(testName, testFunction) {
  const startTime = Date.now();
  
  try {
    console.log(`🧪 Running: ${testName}`);
    const result = await testFunction();
    const duration = Date.now() - startTime;
    
    console.log(`✅ ${testName} - PASSED (${duration}ms)`);
    return { name: testName, passed: true, duration, result };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ ${testName} - FAILED (${duration}ms): ${error.message}`);
    return { name: testName, passed: false, duration, error: error.message };
  }
}

// Test 1: Basic API connectivity
async function testAPIConnectivity() {
  const requiredEnvVars = [
    'OPENROUTER_API_KEY',
    'API_BIRDEYE_API_KEY',
    'ETHERSCAN_API_KEY'
  ];
  
  const missing = requiredEnvVars.filter(key => !process.env[key]);
  if (missing.length > 0) {
    throw new Error(`Missing environment variables: ${missing.join(', ')}`);
  }
  
  // Test OpenRouter connectivity
  const response = await fetch('https://openrouter.ai/api/v1/models', {
    headers: {
      'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
    },
  });
  
  if (!response.ok) {
    throw new Error(`OpenRouter API error: ${response.status}`);
  }
  
  return { apisConfigured: requiredEnvVars.length, openRouterStatus: 'connected' };
}

// Test 2: Token Hunter Agent
async function testTokenHunter() {
  // Import the module dynamically
  const { executeTokenHunter } = await import('./src/ai/agents/token-hunter.js');
  
  const state = {
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    tokenName: 'Wrapped SOL',
    tokenSymbol: 'WSOL',
    errors: [],
  };
  
  const result = await executeTokenHunter(state);
  
  if (!result.tokenHunterResult) {
    throw new Error('Token Hunter did not return result');
  }
  
  return {
    tokenFound: !!result.tokenHunterResult,
    marketCap: result.tokenHunterResult.marketCap,
    source: result.tokenHunterResult.source,
  };
}

// Test 3: Contract Auditor Agent
async function testContractAuditor() {
  const { executeContractAuditor } = await import('./src/ai/agents/contract-auditor.js');
  
  const state = {
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    tokenName: 'Wrapped SOL',
    errors: [],
  };
  
  const result = await executeContractAuditor(state);
  
  if (!result.contractAuditorResult) {
    throw new Error('Contract Auditor did not return result');
  }
  
  return {
    riskScore: result.contractAuditorResult.riskScore,
    confidence: result.contractAuditorResult.confidence,
    contractVerified: result.contractAuditorResult.contractVerified,
  };
}

// Test 4: Full TokenSentinel Workflow
async function testFullWorkflow() {
  const { executeTokenSentinel } = await import('./src/ai/workflows/token-sentinel.js');
  
  const input = {
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    tokenName: 'Wrapped SOL',
    tokenSymbol: 'WSOL',
    priority: 'HIGH',
    skipSocialAnalysis: true, // Skip to avoid rate limits
  };
  
  const result = await executeTokenSentinel(input);
  
  if (!result.overallRiskScore || !result.alertLevel) {
    throw new Error('TokenSentinel workflow incomplete');
  }
  
  // Validate risk score range
  if (result.overallRiskScore < 0 || result.overallRiskScore > 10) {
    throw new Error('Risk score out of range');
  }
  
  return {
    riskScore: result.overallRiskScore,
    alertLevel: result.alertLevel,
    confidence: result.confidence,
    executionTime: result.executionTime,
    agentsExecuted: result.agentsExecuted.length,
  };
}

// Test 5: UI Integration
async function testUIIntegration() {
  const { generateTokenSentinelReport } = await import('./src/ai/flows/token-sentinel-flow.js');
  
  const input = {
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    tokenName: 'Wrapped SOL',
    tokenTicker: 'WSOL',
  };
  
  const result = await generateTokenSentinelReport(input);
  
  const requiredFields = [
    'keyInformation',
    'onChainAnalysis',
    'riskAssessment',
    'scenarioAnalysis',
    'overallAssessment',
  ];
  
  for (const field of requiredFields) {
    if (!result[field]) {
      throw new Error(`Missing required field: ${field}`);
    }
  }
  
  return {
    fieldsPresent: requiredFields.length,
    riskScore: result.riskScore,
    alertLevel: result.alertLevel,
  };
}

// Test 6: Error Handling
async function testErrorHandling() {
  const { executeTokenSentinel } = await import('./src/ai/workflows/token-sentinel.js');
  
  const input = {
    tokenAddress: TEST_TOKENS.INVALID_TOKEN,
    priority: 'LOW',
    skipSocialAnalysis: true,
  };
  
  const result = await executeTokenSentinel(input);
  
  // Should handle error gracefully
  if (result.errors.length === 0) {
    throw new Error('Expected errors for invalid token address');
  }
  
  // Should still return valid structure
  if (!result.alertLevel || typeof result.overallRiskScore !== 'number') {
    throw new Error('Error handling did not return valid structure');
  }
  
  return {
    errorsHandled: result.errors.length,
    gracefulDegradation: true,
    finalRiskScore: result.overallRiskScore,
  };
}

// Main test runner
async function main() {
  console.log('🚀 TokenSentinel Integration Tests');
  console.log('==================================\n');
  
  // Load environment variables
  require('dotenv').config();
  
  const startTime = Date.now();
  const results = [];
  
  // Run all tests
  results.push(await runTest('API Connectivity', testAPIConnectivity));
  results.push(await runTest('Token Hunter Agent', testTokenHunter));
  results.push(await runTest('Contract Auditor Agent', testContractAuditor));
  results.push(await runTest('Full TokenSentinel Workflow', testFullWorkflow));
  results.push(await runTest('UI Integration', testUIIntegration));
  results.push(await runTest('Error Handling', testErrorHandling));
  
  // Calculate summary
  const totalDuration = Date.now() - startTime;
  const passed = results.filter(r => r.passed).length;
  const failed = results.length - passed;
  const successRate = (passed / results.length) * 100;
  
  console.log('\n📊 Test Summary');
  console.log('===============');
  console.log(`Total Tests: ${results.length}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);
  console.log(`Success Rate: ${successRate.toFixed(1)}%`);
  console.log(`Total Duration: ${totalDuration}ms`);
  
  // Detailed results
  console.log('\n📋 Detailed Results');
  console.log('===================');
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${result.name} (${result.duration}ms)`);
    if (result.result) {
      console.log(`   Result: ${JSON.stringify(result.result, null, 2)}`);
    }
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });
  
  // Save report
  const report = {
    timestamp: new Date().toISOString(),
    summary: { total: results.length, passed, failed, successRate, totalDuration },
    results,
  };
  
  const reportsDir = path.join(__dirname, 'test-reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  fs.writeFileSync(
    path.join(reportsDir, `integration-test-${Date.now()}.json`),
    JSON.stringify(report, null, 2)
  );
  
  console.log(`\n📄 Report saved to: ${reportsDir}`);
  
  // Final status
  if (failed === 0) {
    console.log('\n🎉 All tests passed! TokenSentinel is ready.');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Check the details above.');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = { main };
