const https = require('https');

const OPENROUTER_API_KEY = '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';

const data = JSON.stringify({
  model: 'tngtech/deepseek-r1t2-chimera:free',
  messages: [{
    role: 'user',
    content: 'Test message: respond with {"test": "success", "model": "deepseek-r1t2-chimera"}'
  }],
  temperature: 0.1,
  max_tokens: 100
});

const options = {
  hostname: 'openrouter.ai',
  port: 443,
  path: '/api/v1/chat/completions',
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
    'Content-Type': 'application/json',
    'Content-Length': data.length,
    'HTTP-Referer': 'https://tokensentinel.ai',
    'X-Title': 'TokenSentinel AI Test'
  }
};

const req = https.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers: ${JSON.stringify(res.headers)}`);
  
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });
  
  res.on('end', () => {
    console.log('Response body:', body);
    try {
      const parsed = JSON.parse(body);
      console.log('Parsed response:', JSON.stringify(parsed, null, 2));
    } catch (e) {
      console.log('Failed to parse JSON:', e.message);
    }
  });
});

req.on('error', (e) => {
  console.error(`Request error: ${e.message}`);
});

req.write(data);
req.end();
