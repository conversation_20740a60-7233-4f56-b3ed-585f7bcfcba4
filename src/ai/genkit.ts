import {genkit} from 'genkit';
import {googleAI} from '@genkit-ai/googleai';

// OpenRouter configuration for DeepSeek R1
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

export const ai = genkit({
  plugins: [googleAI()],
  model: 'googleai/gemini-2.0-flash',
});

// OpenRouter client for DeepSeek R1
export const createOpenRouterClient = () => {
  if (!OPENROUTER_API_KEY) {
    throw new Error('OPENROUTER_API_KEY is not configured');
  }

  return {
    apiKey: OPENROUTER_API_KEY,
    baseURL: 'https://openrouter.ai/api/v1',
    model: 'deepseek/deepseek-r1:free', // Free tier DeepSeek R1
  };
};
