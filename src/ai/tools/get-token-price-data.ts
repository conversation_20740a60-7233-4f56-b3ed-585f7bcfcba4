'use server';
/**
 * @fileOverview A Genkit tool for fetching historical price data for a cryptocurrency.
 */

import {ai} from '@/ai/genkit';
import {TOKEN_MAP} from '@/lib/kraken-pairs';
import {z} from 'genkit';

// Helper function to find the Kraken ticker from the common symbol
function getKrakenTicker(symbol: string): string | null {
    const upperSymbol = symbol.toUpperCase();
    for (const key in TOKEN_MAP) {
        if (TOKEN_MAP[key].symbol === upperSymbol) {
            // Kraken API expects the pair without the slash, e.g., XBTUSD
            return key.replace('/', '');
        }
    }
    // Fallback for symbols not in the map
    const commonKrakenSymbol = upperSymbol === 'BTC' ? 'XBT' : upperSymbol;
    return `${commonKrakenSymbol}USD`;
}


export const getTokenPriceData = ai.defineTool(
  {
    name: 'getTokenPriceData',
    description: 'Retrieves recent OHLC (Open, High, Low, Close) price data for a given cryptocurrency ticker.',
    inputSchema: z.object({
      tokenTicker: z.string().describe('The ticker symbol of the token (e.g., "BTC", "ETH").'),
    }),
    outputSchema: z.object({
      priceData: z.array(z.object({
          time: z.number().describe("The timestamp of the candle."),
          open: z.string(),
          high: z.string(),
          low: z.string(),
          close: z.string(),
          volume: z.string(),
      })).describe("An array of OHLC data points for the last 72 hours (72 candles).")
    }),
  },
  async (input) => {
    console.log(`[getTokenPriceData] Fetching price data for ${input.tokenTicker}`);
    
    const krakenPair = getKrakenTicker(input.tokenTicker);
    if (!krakenPair) {
        throw new Error(`Could not find a valid Kraken pair for ticker: ${input.tokenTicker}`);
    }

    // Fetch hourly data for the last 72 hours (3 days)
    const url = `https://api.kraken.com/0/public/OHLC?pair=${krakenPair}&interval=60`;

    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Kraken API request failed with status: ${response.status}`);
        }
        const data = await response.json();

        if (data.error && data.error.length > 0) {
            console.error("Error fetching historical data from Kraken:", data.error);
            throw new Error(`Kraken API error: ${data.error.join(', ')}`);
        }

        const resultKey = Object.keys(data.result)[0];
        const ohlc = data.result[resultKey];

        const priceData = ohlc.slice(-72).map((d: any) => ({
            time: d[0],
            open: d[1],
            high: d[2],
            low: d[3],
            close: d[4],
            volume: d[6],
        }));

        return { priceData };

    } catch (error) {
      console.error("Failed to fetch historical data from Kraken:", error);
      // Return empty array on failure so the AI can still proceed
      return { priceData: [] };
    }
  }
);
