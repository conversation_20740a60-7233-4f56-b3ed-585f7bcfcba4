'use server';
/**
 * @fileOverview A Genkit tool for fetching on-chain data for Solana tokens using the Birdeye API.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const BIRDEYE_API_KEY = process.env.API_BIRDEYE_API_KEY;

export const getSolanaTokenData = ai.defineTool(
  {
    name: 'getSolanaTokenData',
    description: 'Retrieves detailed on-chain information for a given Solana token address from the Birdeye API.',
    inputSchema: z.object({
      tokenAddress: z.string().describe('The contract address of the Solana token.'),
    }),
    outputSchema: z.object({
        liquidity: z.number().optional(),
        volume24h: z.number().optional(),
        price: z.number().optional(),
        priceChange24hPercent: z.number().optional(),
    }).describe("Key on-chain metrics for a Solana token."),
  },
  async (input) => {
    if (!BIRDEYE_API_KEY) {
      throw new Error("Birdeye API key not configured.");
    }
    if (!input.tokenAddress) {
      return {};
    }

    const url = `https://public-api.birdeye.so/defi/token_overview?address=${input.tokenAddress}`;
    
    try {
        const response = await fetch(url, {
            headers: { 'X-API-KEY': BIRDEYE_API_KEY }
        });

        if (!response.ok) {
             const errorText = await response.text();
             console.error(`Birdeye API request failed with status: ${response.status}`, errorText);
             // Don't throw, allow AI to continue
             return { error: `Failed to fetch data from Birdeye API: ${response.statusText}` };
        }

        const data = await response.json();

        if (!data.success || !data.data) {
            return { error: 'No data returned from Birdeye for this address.' };
        }

        const tokenData = data.data;

        return {
            liquidity: tokenData.liquidity,
            volume24h: tokenData.v24h,
            price: tokenData.price,
            priceChange24hPercent: tokenData.priceChange24h,
        };

    } catch (error) {
        console.error("Failed to fetch data from Birdeye:", error);
        return { error: 'An exception occurred while fetching from Birdeye.' };
    }
  }
);
