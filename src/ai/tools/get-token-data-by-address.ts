'use server';
/**
 * @fileOverview A Genkit tool for fetching comprehensive on-chain and market data for a Solana token using its address.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const BIRDEYE_API_KEY = process.env.API_BIRDEYE_API_KEY;

export const getTokenDataByAddress = ai.defineTool(
  {
    name: 'getTokenDataByAddress',
    description: 'Retrieves detailed on-chain and market information for a given Solana token address from the Birdeye API. This should be the first tool called to identify a token and get its core data.',
    inputSchema: z.object({
      tokenAddress: z.string().describe('The contract address of the Solana token.'),
    }),
    outputSchema: z.object({
        name: z.string().optional(),
        symbol: z.string().optional(),
        description: z.string().optional(),
        price: z.number().optional(),
        liquidity: z.number().optional(),
        volume24h: z.number().optional(),
        priceChange24hPercent: z.number().optional(),
        holders: z.number().optional(),
        marketCap: z.number().optional(),
    }).describe("Comprehensive data for a Solana token."),
  },
  async (input) => {
    if (!BIRDEYE_API_KEY) {
      throw new Error("Birdeye API key not configured.");
    }
    if (!input.tokenAddress) {
      // Return an empty object if no address is provided, the AI should handle this.
      return {};
    }

    // Birdeye has different endpoints, we'll try the multi-token overview first
    // as it's often more reliable for various token types.
    const url = `https://public-api.birdeye.so/defi/token_overview?address=${input.tokenAddress}`;
    
    try {
        const response = await fetch(url, {
            headers: { 'X-API-KEY': BIRDEYE_API_KEY }
        });

        if (!response.ok) {
             const errorText = await response.text();
             console.error(`Birdeye API request failed with status: ${response.status}`, errorText);
             // Let the AI know the fetch failed.
             return { description: `Failed to fetch data from Birdeye API: ${response.statusText}` };
        }

        const data = await response.json();

        if (!data.success || !data.data) {
            console.log(`No data returned from Birdeye for address: ${input.tokenAddress}`);
            return { description: 'No data found for this address on Birdeye.' };
        }

        const tokenData = data.data;

        // Map the Birdeye response to our schema
        return {
            name: tokenData.name,
            symbol: tokenData.symbol,
            description: tokenData.description, // Note: Birdeye often lacks a description
            price: tokenData.price,
            liquidity: tokenData.liquidity,
            volume24h: tokenData.v24h,
            priceChange24hPercent: tokenData.priceChange24h,
            holders: tokenData.holders,
            marketCap: tokenData.mc,
        };

    } catch (error) {
        console.error("Failed to fetch data from Birdeye:", error);
        return { description: 'An exception occurred while fetching data from Birdeye.' };
    }
  }
);
