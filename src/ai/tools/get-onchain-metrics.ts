'use server';
/**
 * @fileOverview A Genkit tool for fetching the latest results from a Dune Analytics query.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const DUNE_API_KEY = process.env.DUNE_API_KEY;

// For this demo, we are mapping keywords to specific, public Dune query IDs.
// A more advanced implementation could use the Dune API to search for queries.
const DUNE_QUERY_MAP: Record<string, number> = {
    "dex": 3778313, // Example: "Weekly DEX Volume" - https://dune.com/queries/3778313
    "nft": 3475440, // Example: "Daily NFT Market Volume" - https://dune.com/queries/3475440
    "lending": 3729313, // Example: "Total Lending Deposits" - https://dune.com/queries/3729313
};

export const getOnChainMetrics = ai.defineTool(
  {
    name: 'getOnChainMetrics',
    description: 'Retrieves the latest results from a relevant, pre-defined Dune Analytics query based on a keyword (e.g., "dex", "nft", "lending"). Provides high-level on-chain metrics.',
    inputSchema: z.object({
      category: z.string().describe('The category of metrics to fetch. Supported: "dex", "nft", "lending".'),
    }),
    outputSchema: z.object({
      sourceName: z.string(),
      lastUpdatedAt: z.string(),
      results: z.array(z.record(z.any())).describe("An array of result rows from the query."),
    }),
  },
  async (input) => {
    if (!DUNE_API_KEY) {
      throw new Error("Dune API key is not configured.");
    }
    
    const queryId = DUNE_QUERY_MAP[input.category.toLowerCase()];
    if (!queryId) {
        return {
            sourceName: 'Dune Analytics',
            lastUpdatedAt: new Date().toISOString(),
            results: [{ "error": `No query found for category: ${input.category}` }]
        }
    }

    const url = `https://api.dune.com/api/v1/query/${queryId}/results`;

    try {
        const response = await fetch(url, {
            headers: { 'x-dune-api-key': DUNE_API_KEY }
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`Dune API request failed with status: ${response.status}`, errorText);
            throw new Error(`Dune API request failed: ${response.statusText}`);
        }

        const data = await response.json();
        const execution = data.result;

        return {
            sourceName: "Dune Analytics",
            lastUpdatedAt: data.execution_ended_at,
            results: execution.rows,
        };

    } catch (error) {
        console.error("Failed to fetch data from Dune Analytics:", error);
        return {
            sourceName: 'Dune Analytics',
            lastUpdatedAt: new Date().toISOString(),
            results: [{ "error": "Failed to retrieve data from Dune API." }]
        }
    }
  }
);
