'use server';
/**
 * @fileOverview A Genkit tool for fetching recent news about a cryptocurrency.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

export const getTokenNews = ai.defineTool(
  {
    name: 'getTokenNews',
    description: 'Retrieves recent news articles for a given cryptocurrency token name.',
    inputSchema: z.object({
      tokenName: z.string().describe('The full name of the token (e.g., "Bitcoin", "Ethereum").'),
    }),
    outputSchema: z.object({
      articles: z.array(z.object({
        headline: z.string(),
        source: z.string(),
        sentiment: z.enum(['positive', 'negative', 'neutral']),
      })),
    }),
  },
  async (input) => {
    console.log(`[getTokenNews] Fetching news for ${input.tokenName}`);
    // In a real application, you would use a news API here.
    // For this demo, we'll return a set of realistic, pre-defined sample articles.
    const sampleNews: Record<string, any[]> = {
      'Bitcoin': [
        { headline: "Bitcoin Hits New All-Time High Amidst Institutional Adoption", source: "Crypto Times", sentiment: "positive" },
        { headline: "Regulators Announce Scrutiny of Crypto Markets; Bitcoin Dips", source: "Financial World", sentiment: "negative" },
        { headline: "Major Tech CEO Endorses Bitcoin as a 'Hedge Against Inflation'", source: "Tech Chronicle", sentiment: "positive" },
      ],
      'Ethereum': [
        { headline: "Ethereum's 'Dencun' Upgrade Goes Live, Slashing Layer-2 Fees", source: "ETH News", sentiment: "positive" },
        { headline: "Vitalik Buterin Outlines Future Roadmap for Ethereum Scalability", source: "Crypto Globe", sentiment: "positive" },
        { headline: "Gas Fees Spike on Ethereum Mainnet Amidst NFT Minting Frenzy", source: "NFT Today", sentiment: "neutral" },
      ],
      'Solana': [
        { headline: "Solana Network Experiences Outage, Team Works on a Fix", source: "Chain Watch", sentiment: "negative" },
        { headline: "Breakout Success of New Meme Coin Puts Spotlight on Solana Ecosystem", source: "Coin Telegraph", sentiment: "positive" },
        { headline: "Solana DeFi Projects See Influx of Users from Ethereum", source: "DeFi Pulse", sentiment: "positive" },
      ]
    };

    const articles = sampleNews[input.tokenName] || [
        { headline: `No specific major news found for ${input.tokenName}. Market sentiment remains neutral.`, source: "General Feed", sentiment: "neutral" },
    ];
    
    return { articles };
  }
);
