'use server';

/**
 * @fileOverview Orchestrates a multi-agent analysis of a cryptocurrency token.
 *
 * - generateTokenReport - The main function that runs the analysis.
 * - GenerateTokenReportInput - The input type for the generateTokenReport function.
 * - GenerateTokenReportOutput - The return type for the generateTokenReport function.
 */

import {ai} from '@/ai/genkit';
import {getOnChainMetrics} from '@/ai/tools/get-onchain-metrics';
import {getTokenDataByAddress} from '@/ai/tools/get-token-data-by-address';
import {getTokenNews} from '@/ai/tools/get-token-news';
import {z} from 'genkit';

const GenerateTokenReportInputSchema = z.object({
  // The token address is now the primary identifier
  tokenAddress: z.string().describe('The contract address of the token.'),
  tokenName: z
    .string()
    .optional()
    .describe('The name of the cryptocurrency token.'),
  tokenTicker: z
    .string()
    .optional()
    .describe('The ticker symbol of the cryptocurrency token (e.g., BTC, ETH).'),
});
export type GenerateTokenReportInput = z.infer<
  typeof GenerateTokenReportInputSchema
>;

const GenerateTokenReportOutputSchema = z.object({
  keyInformation: z
    .string()
    .describe(
      'Key information about the token, such as its purpose, technology, and team. Sourced from its description and on-chain data.'
    ),
  onChainAnalysis: z
    .string()
    .describe(
      'An analysis of on-chain activity, such as transaction volume, liquidity changes, and holder distribution. Synthesized from tools like Birdeye and Dune.'
    ),
  riskAssessment: z
    .string()
    .describe(
      'A risk assessment of the token, considering factors like liquidity, holder concentration, and potential for rug pulls.'
    ),
  scenarioAnalysis: z.object({
    bullish: z
      .string()
      .describe(
        "A potential bullish (positive) scenario for the token's price, based on news, market trends, and on-chain data."
      ),
    bearish: z
      .string()
      .describe(
        "A potential bearish (negative) scenario for the token's price, based on news, market trends, and on-chain data."
      ),
  }),
  overallAssessment: z
    .string()
    .describe(
      'A final, overall assessment of the token, combining all the generated information.'
    ),
  // To pass the determined name and symbol back to the UI
  tokenName: z
    .string()
    .describe(
      'The name of the token, as determined by the on-chain data lookup.'
    ),
  tokenSymbol: z
    .string()
    .describe(
      'The symbol of the token, as determined by the on-chain data lookup.'
    ),
});
export type GenerateTokenReportOutput = z.infer<
  typeof GenerateTokenReportOutputSchema
>;

export async function generateTokenReport(
  input: GenerateTokenReportInput
): Promise<GenerateTokenReportOutput> {
  return generateTokenReportFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateTokenReportPrompt',
  input: {schema: GenerateTokenReportInputSchema},
  output: {schema: GenerateTokenReportOutputSchema},
  tools: [getTokenNews, getTokenDataByAddress, getOnChainMetrics],
  prompt: `You are an expert cryptocurrency analyst, acting as an orchestrator for a team of specialist agents. Your goal is to generate a comprehensive report for the token with address: {{tokenAddress}}.

You have access to the following specialist agent tools:
1. \`getTokenDataByAddress\`: The primary tool to get essential data for any Solana token, including its name, symbol, price, and other on-chain metrics like liquidity.
2. \`getTokenNews\`: To get recent news articles related to a token's name.
3. \`getOnChainMetrics\`: To get broader on-chain metrics from Dune Analytics for protocols (e.g., DEX volumes). This is less likely to be useful for brand new tokens.

Your process is as follows:
1.  **CRUCIAL FIRST STEP**: Call \`getTokenDataByAddress\` with the provided \`{{tokenAddress}}\`. This is the most important step.
2.  **VALIDATE RESPONSE**: If the \`getTokenDataByAddress\` tool returns no data, an error, or a description saying it failed, you MUST STOP. In this case, set the \`tokenName\` to "Unknown Name", \`tokenSymbol\` to "Unknown Symbol", and for the \`keyInformation\` field, you MUST explain that the token could not be found at the given address and that no analysis can be performed. Do not proceed to the next steps.
3.  **GATHER NEWS (if successful)**: If you successfully retrieved data in step 1, use the token name you just received to call the \`getTokenNews\` tool.
4.  **SYNTHESIZE AND REPORT**: Combine all the information (on-chain data from Birdeye, news) to build a complete report.
5.  **ON-CHAIN ANALYSIS**: Your "On-Chain Analysis" should be based *directly* on the data from \`getTokenDataByAddress\`. Comment on the liquidity, 24h volume, number of holders, and any other relevant metrics provided.
6.  **RISK ASSESSMENT**: This is critical for new tokens. Based on the on-chain data, assess the risk. Pay close attention to low liquidity, high creator balance, or a small number of holders as potential red flags for a 'rug pull'.
7.  **SCENARIO ANALYSIS**: Use price trends, news sentiment, and on-chain activity to outline a plausible bullish scenario and a plausible bearish scenario. Do NOT give financial advice or predict a specific price. Frame it as "A bullish scenario could see..." or "In a bearish scenario, factors like...".
8.  **OVERALL ASSESSMENT**: Provide a final "Overall Assessment" that summarizes the token's potential and risks.

Generate the report with the following sections: Key Information, On-Chain Analysis, Risk Assessment, Scenario Analysis (with Bullish and Bearish sub-sections), and Overall Assessment. Be concise and professional. Use the name and symbol from the tool output in your final response.`,
});

const generateTokenReportFlow = ai.defineFlow(
  {
    name: 'generateTokenReportFlow',
    inputSchema: GenerateTokenReportInputSchema,
    outputSchema: GenerateTokenReportOutputSchema,
  },
  async (input) => {
    const {output} = await prompt(input);
    return output!;
  }
);
