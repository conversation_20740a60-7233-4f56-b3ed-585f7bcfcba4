'use server';

/**
 * @fileOverview TokenSentinel Integration Flow - Connects multi-agent analysis with UI
 * Replaces the existing generate-token-report.ts with enhanced rug detection capabilities
 */

import { z } from 'zod';
import { executeTokenSentinel, type TokenSentinelInput, type TokenSentinelOutput } from '../workflows/token-sentinel';
import { sendTokenAlert } from '../../lib/telegram-alerts';

// Input schema matching existing interface
const TokenSentinelFlowInputSchema = z.object({
  tokenAddress: z.string().describe('The contract address of the token.'),
  tokenName: z.string().optional().describe('The name of the cryptocurrency token.'),
  tokenTicker: z.string().optional().describe('The ticker symbol of the cryptocurrency token (e.g., BTC, ETH).'),
});

export type TokenSentinelFlowInput = z.infer<typeof TokenSentinelFlowInputSchema>;

// Output schema matching existing UI expectations
const TokenSentinelFlowOutputSchema = z.object({
  keyInformation: z.string().describe('Key information about the token, including risk assessment and basic metrics.'),
  onChainAnalysis: z.string().describe('Detailed on-chain analysis including liquidity, holder distribution, and transaction patterns.'),
  riskAssessment: z.string().describe('Comprehensive rug pull risk assessment with specific indicators and confidence levels.'),
  scenarioAnalysis: z.object({
    bullish: z.string().describe('Bullish scenario based on positive indicators and market conditions.'),
    bearish: z.string().describe('Bearish scenario highlighting risks and potential negative outcomes.'),
  }),
  overallAssessment: z.string().describe('Final overall assessment combining all analysis results.'),
  tokenName: z.string().describe('The name of the token as determined by analysis.'),
  tokenSymbol: z.string().describe('The symbol of the token as determined by analysis.'),
  
  // Enhanced fields for TokenSentinel
  riskScore: z.number().min(0).max(10).describe('Numerical risk score from 0 (safe) to 10 (extreme risk).'),
  rugPullProbability: z.number().min(0).max(1).describe('Probability of rug pull from 0 to 1.'),
  alertLevel: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).describe('Alert level for the token.'),
  priceDirection: z.enum(['UP', 'DOWN', 'SIDEWAYS']).describe('Predicted price direction.'),
  confidence: z.number().min(0).max(1).describe('Confidence level of the analysis.'),
});

export type TokenSentinelFlowOutput = z.infer<typeof TokenSentinelFlowOutputSchema>;

/**
 * Main TokenSentinel flow function - Enhanced version of generateTokenReport
 */
export async function generateTokenSentinelReport(
  input: TokenSentinelFlowInput
): Promise<TokenSentinelFlowOutput> {
  try {
    console.log(`[TokenSentinelFlow] Starting enhanced analysis for ${input.tokenAddress}`);
    
    // Convert input to TokenSentinel format
    const sentinelInput: TokenSentinelInput = {
      tokenAddress: input.tokenAddress,
      tokenName: input.tokenName,
      tokenSymbol: input.tokenTicker,
      priority: 'MEDIUM',
      skipSocialAnalysis: false,
    };
    
    // Execute TokenSentinel multi-agent analysis
    const sentinelResult = await executeTokenSentinel(sentinelInput);
    
    // Send Telegram alert if warranted
    if (sentinelResult.shouldAlert) {
      await sendTokenAlert(sentinelResult).catch(error => {
        console.error('[TokenSentinelFlow] Alert sending failed:', error);
      });
    }
    
    // Convert result to UI-compatible format
    const uiResult = convertToUIFormat(sentinelResult);
    
    console.log(`[TokenSentinelFlow] Analysis completed - Risk: ${uiResult.riskScore}/10`);
    
    return uiResult;
    
  } catch (error) {
    console.error('[TokenSentinelFlow] Analysis failed:', error);
    
    // Return error state in expected format
    return {
      keyInformation: `Analysis failed for token ${input.tokenAddress}. ${error instanceof Error ? error.message : 'Unknown error occurred.'}`,
      onChainAnalysis: 'Unable to perform on-chain analysis due to system error.',
      riskAssessment: 'CRITICAL: Analysis system failure - treat as maximum risk.',
      scenarioAnalysis: {
        bullish: 'Unable to determine bullish scenario due to analysis failure.',
        bearish: 'System failure indicates maximum risk - avoid this token.',
      },
      overallAssessment: 'Analysis failed. Recommend avoiding this token until system is restored.',
      tokenName: input.tokenName || 'Unknown Token',
      tokenSymbol: input.tokenTicker || 'UNKNOWN',
      riskScore: 10,
      rugPullProbability: 1,
      alertLevel: 'CRITICAL',
      priceDirection: 'DOWN',
      confidence: 0,
    };
  }
}

/**
 * Convert TokenSentinel result to UI-compatible format
 */
function convertToUIFormat(result: TokenSentinelOutput): TokenSentinelFlowOutput {
  // Generate key information summary
  const keyInformation = generateKeyInformation(result);
  
  // Generate on-chain analysis text
  const onChainAnalysis = generateOnChainAnalysis(result);
  
  // Generate risk assessment text
  const riskAssessment = generateRiskAssessment(result);
  
  // Generate scenario analysis
  const scenarioAnalysis = generateScenarioAnalysis(result);
  
  // Generate overall assessment
  const overallAssessment = generateOverallAssessment(result);
  
  return {
    keyInformation,
    onChainAnalysis,
    riskAssessment,
    scenarioAnalysis,
    overallAssessment,
    tokenName: result.tokenName,
    tokenSymbol: result.tokenSymbol,
    riskScore: result.overallRiskScore,
    rugPullProbability: result.rugPullProbability,
    alertLevel: result.alertLevel,
    priceDirection: result.priceDirection,
    confidence: result.confidence,
  };
}

/**
 * Generate key information summary
 */
function generateKeyInformation(result: TokenSentinelOutput): string {
  let info = `${result.tokenName} (${result.tokenSymbol}) is a cryptocurrency token with the following characteristics:\n\n`;
  
  if (result.tokenHunterResult) {
    const age = result.tokenHunterResult.ageHours;
    info += `• Age: ${age < 1 ? `${Math.round(age * 60)} minutes` : `${age.toFixed(1)} hours`} old\n`;
    info += `• Market Cap: $${result.tokenHunterResult.marketCap.toLocaleString()}\n`;
    info += `• 24h Volume: $${result.tokenHunterResult.volume24h.toLocaleString()}\n`;
    info += `• Source: ${result.tokenHunterResult.source}\n\n`;
  }
  
  info += `Risk Assessment: ${result.overallRiskScore.toFixed(1)}/10 (${result.alertLevel} risk)\n`;
  info += `Rug Pull Probability: ${(result.rugPullProbability * 100).toFixed(1)}%\n`;
  info += `Analysis Confidence: ${(result.confidence * 100).toFixed(1)}%\n`;
  info += `Predicted Price Direction: ${result.priceDirection}`;
  
  return info;
}

/**
 * Generate on-chain analysis text
 */
function generateOnChainAnalysis(result: TokenSentinelOutput): string {
  let analysis = 'On-chain analysis reveals the following metrics:\n\n';
  
  if (result.onChainAnalystResult) {
    const onchain = result.onChainAnalystResult;
    
    analysis += `Holder Distribution:\n`;
    analysis += `• Top holder concentration: ${(onchain.holderConcentration * 100).toFixed(1)}%\n`;
    analysis += `• ${onchain.holderConcentration > 0.8 ? '🔴 HIGH RISK' : onchain.holderConcentration > 0.6 ? '🟡 MEDIUM RISK' : '🟢 LOW RISK'} - Concentration level\n\n`;
    
    analysis += `Liquidity Metrics:\n`;
    analysis += `• Current liquidity: $${onchain.liquidityUSD.toLocaleString()}\n`;
    analysis += `• 24h change: ${onchain.liquidityChange24h > 0 ? '+' : ''}${(onchain.liquidityChange24h * 100).toFixed(1)}%\n`;
    analysis += `• ${onchain.liquidityUSD < 10000 ? '🔴 LOW LIQUIDITY' : onchain.liquidityUSD < 50000 ? '🟡 MEDIUM LIQUIDITY' : '🟢 GOOD LIQUIDITY'}\n\n`;
    
    analysis += `Transaction Activity:\n`;
    analysis += `• Large transactions (>$10k): ${onchain.largeTransactions}\n`;
    analysis += `• Creator balance: ${(onchain.creatorBalance * 100).toFixed(1)}%\n`;
    analysis += `• Suspicious activity: ${onchain.suspiciousActivity ? '🔴 DETECTED' : '🟢 NONE DETECTED'}\n`;
  } else {
    analysis += 'On-chain data could not be retrieved. This may indicate:\n';
    analysis += '• Very new token not yet indexed\n';
    analysis += '• API connectivity issues\n';
    analysis += '• Token may not exist or be invalid\n';
  }
  
  return analysis;
}

/**
 * Generate risk assessment text
 */
function generateRiskAssessment(result: TokenSentinelOutput): string {
  let assessment = `RISK LEVEL: ${result.alertLevel} (${result.overallRiskScore.toFixed(1)}/10)\n\n`;
  
  assessment += 'Risk Factors Identified:\n';
  
  if (result.contractAuditorResult) {
    const contract = result.contractAuditorResult;
    
    if (contract.hiddenMintDetected) {
      assessment += '🔴 CRITICAL: Hidden mint function detected - tokens can be created at will\n';
    }
    
    if (!contract.ownershipRenounced) {
      assessment += '🟡 WARNING: Ownership not renounced - centralized control remains\n';
    }
    
    if (contract.blacklistFunction) {
      assessment += '🔴 HIGH RISK: Blacklist function present - users can be blocked from trading\n';
    }
    
    if (contract.pausable) {
      assessment += '🟡 WARNING: Contract is pausable - trading can be halted\n';
    }
    
    if (!contract.contractVerified) {
      assessment += '🟡 WARNING: Contract source code not verified\n';
    }
  }
  
  if (result.socialSentimentResult) {
    const social = result.socialSentimentResult;
    
    if (social.botActivityDetected) {
      assessment += '🔴 HIGH RISK: Bot activity detected in social media promotion\n';
    }
    
    if (social.coordinatedPromotion) {
      assessment += '🟡 WARNING: Coordinated promotion campaign detected\n';
    }
    
    if (social.mentionCount < 10) {
      assessment += '🟡 WARNING: Very low social media mentions - possible fake hype\n';
    }
  }
  
  assessment += `\nConfidence Level: ${(result.confidence * 100).toFixed(1)}%\n`;
  assessment += `Recommendation: ${result.recommendedAction || 'Proceed with extreme caution'}`;
  
  return assessment;
}

/**
 * Generate scenario analysis
 */
function generateScenarioAnalysis(result: TokenSentinelOutput): {
  bullish: string;
  bearish: string;
} {
  let bullish = 'Bullish Scenario:\n';
  let bearish = 'Bearish Scenario:\n';
  
  // Bullish factors
  if (result.overallRiskScore < 4) {
    bullish += '• Low risk score suggests legitimate project\n';
  }
  
  if (result.contractAuditorResult?.ownershipRenounced) {
    bullish += '• Ownership renounced reduces centralization risk\n';
  }
  
  if (result.onChainAnalystResult?.liquidityUSD > 50000) {
    bullish += '• Strong liquidity supports price stability\n';
  }
  
  if (result.socialSentimentResult?.sentimentScore > 0.3) {
    bullish += '• Positive social sentiment indicates community support\n';
  }
  
  if (result.priceDirection === 'UP') {
    bullish += '• Technical indicators suggest upward price movement\n';
  }
  
  bullish += '\nIf these factors hold, the token could see sustained growth and community adoption.';
  
  // Bearish factors
  if (result.overallRiskScore > 6) {
    bearish += '• High risk score indicates multiple red flags\n';
  }
  
  if (result.contractAuditorResult?.hiddenMintDetected) {
    bearish += '• Hidden mint function allows unlimited token creation\n';
  }
  
  if (result.onChainAnalystResult?.holderConcentration > 0.8) {
    bearish += '• High holder concentration enables market manipulation\n';
  }
  
  if (result.socialSentimentResult?.botActivityDetected) {
    bearish += '• Bot activity suggests artificial hype\n';
  }
  
  if (result.rugPullProbability > 0.7) {
    bearish += '• High rug pull probability based on historical patterns\n';
  }
  
  bearish += '\nThese factors could lead to a sudden price collapse or complete rug pull, resulting in total loss of investment.';
  
  return { bullish, bearish };
}

/**
 * Generate overall assessment
 */
function generateOverallAssessment(result: TokenSentinelOutput): string {
  let assessment = `Overall Assessment for ${result.tokenName} (${result.tokenSymbol}):\n\n`;
  
  if (result.alertLevel === 'CRITICAL') {
    assessment += '🚨 CRITICAL RISK - AVOID COMPLETELY\n';
    assessment += 'This token exhibits multiple severe risk factors that strongly indicate a rug pull or scam. ';
    assessment += 'Investment is extremely dangerous and likely to result in total loss.';
  } else if (result.alertLevel === 'HIGH') {
    assessment += '⚠️ HIGH RISK - EXTREME CAUTION REQUIRED\n';
    assessment += 'This token shows significant risk factors that suggest potential for rug pull or manipulation. ';
    assessment += 'Only experienced traders should consider, and only with funds they can afford to lose completely.';
  } else if (result.alertLevel === 'MEDIUM') {
    assessment += '⚡ MEDIUM RISK - PROCEED WITH CAUTION\n';
    assessment += 'This token has some concerning factors but may be legitimate. ';
    assessment += 'Thorough research and risk management are essential before investing.';
  } else {
    assessment += '✅ LOW RISK - POTENTIAL OPPORTUNITY\n';
    assessment += 'This token shows relatively few risk factors and may represent a legitimate investment opportunity. ';
    assessment += 'However, all cryptocurrency investments carry inherent risks.';
  }
  
  assessment += `\n\nKey Metrics:\n`;
  assessment += `• Risk Score: ${result.overallRiskScore.toFixed(1)}/10\n`;
  assessment += `• Rug Pull Probability: ${(result.rugPullProbability * 100).toFixed(1)}%\n`;
  assessment += `• Price Direction: ${result.priceDirection}\n`;
  assessment += `• Analysis Confidence: ${(result.confidence * 100).toFixed(1)}%\n`;
  
  assessment += `\nThis analysis was completed in ${result.executionTime}ms using ${result.agentsExecuted.length} specialized AI agents.`;
  
  if (result.errors.length > 0) {
    assessment += `\n\nNote: ${result.errors.length} error(s) occurred during analysis, which may affect accuracy.`;
  }
  
  assessment += '\n\n⚠️ DISCLAIMER: This analysis is for informational purposes only and should not be considered financial advice. ';
  assessment += 'Always conduct your own research and never invest more than you can afford to lose.';
  
  return assessment;
}
