'use server';
/**
 * @fileOverview Summarizes news articles related to a specific cryptocurrency token.
 *
 * - summarizeTokenNews - A function that summarizes the latest news articles related to a specific cryptocurrency token.
 * - SummarizeTokenNewsInput - The input type for the summarizeTokenNews function.
 * - SummarizeTokenNewsOutput - The return type for the summarizeTokenNews function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SummarizeTokenNewsInputSchema = z.object({
  tokenName: z.string().describe('The name of the cryptocurrency token.'),
  newsArticles: z.array(z.string()).describe('An array of news articles related to the token.'),
});
export type SummarizeTokenNewsInput = z.infer<typeof SummarizeTokenNewsInputSchema>;

const SummarizeTokenNewsOutputSchema = z.object({
  summary: z.string().describe('A summary of the news articles related to the token, including sentiment analysis and potential impact on price.'),
});
export type SummarizeTokenNewsOutput = z.infer<typeof SummarizeTokenNewsOutputSchema>;

export async function summarizeTokenNews(input: SummarizeTokenNewsInput): Promise<SummarizeTokenNewsOutput> {
  return summarizeTokenNewsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'summarizeTokenNewsPrompt',
  input: {schema: SummarizeTokenNewsInputSchema},
  output: {schema: SummarizeTokenNewsOutputSchema},
  prompt: `You are an AI assistant that summarizes news articles related to a specific cryptocurrency token.

  Token Name: {{{tokenName}}}
  News Articles:
  {{#each newsArticles}}
  - {{{this}}}
  {{/each}}

  Please provide a concise summary of the news articles, including sentiment analysis and potential impact on the token's price.`,
});

const summarizeTokenNewsFlow = ai.defineFlow(
  {
    name: 'summarizeTokenNewsFlow',
    inputSchema: SummarizeTokenNewsInputSchema,
    outputSchema: SummarizeTokenNewsOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
