'use server';

/**
 * @fileOverview TokenSentinel Supervisor Agent - Orchestrates multi-agent rug detection workflow
 * Uses LangGraph pattern with OpenRouter DeepSeek R1 for cost-effective analysis
 */

import { z } from 'zod';

// Agent state schema for LangGraph workflow
export const TokenAnalysisState = z.object({
  tokenAddress: z.string(),
  tokenName: z.string().optional(),
  tokenSymbol: z.string().optional(),
  
  // Agent results
  tokenHunterResult: z.object({
    isNewToken: z.boolean(),
    ageHours: z.number(),
    marketCap: z.number(),
    volume24h: z.number(),
    source: z.string(), // 'pumpfun', 'dexscreener', etc.
  }).optional(),
  
  contractAuditorResult: z.object({
    riskScore: z.number().min(0).max(10),
    hiddenMintDetected: z.boolean(),
    ownershipRenounced: z.boolean(),
    liquidityLocked: z.boolean(),
    suspiciousFunctions: z.array(z.string()),
    confidence: z.number().min(0).max(1),
  }).optional(),
  
  onChainAnalystResult: z.object({
    holderConcentration: z.number(), // Top 10 holders percentage
    liquidityUSD: z.number(),
    liquidityChange24h: z.number(),
    largeTransactions: z.number(), // Count of >$10k transactions
    creatorBalance: z.number(), // Creator's token percentage
    suspiciousActivity: z.boolean(),
  }).optional(),
  
  socialSentimentResult: z.object({
    mentionCount: z.number(),
    sentimentScore: z.number().min(-1).max(1),
    botActivityDetected: z.boolean(),
    influencerMentions: z.number(),
    coordinatedPromotion: z.boolean(),
  }).optional(),
  
  // Final analysis
  overallRiskScore: z.number().min(0).max(10).optional(),
  rugPullProbability: z.number().min(0).max(1).optional(),
  priceDirection: z.enum(['UP', 'DOWN', 'SIDEWAYS']).optional(),
  confidence: z.number().min(0).max(1).optional(),
  alertLevel: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
  
  // Workflow control
  currentAgent: z.string().optional(),
  nextAgent: z.string().optional(),
  completed: z.boolean().default(false),
  errors: z.array(z.string()).default([]),
});

export type TokenAnalysisState = z.infer<typeof TokenAnalysisState>;

// Agent routing logic
export const determineNextAgent = (state: TokenAnalysisState): string | null => {
  // Sequential agent execution with error handling
  if (!state.tokenHunterResult) return 'token_hunter';
  if (!state.contractAuditorResult) return 'contract_auditor';
  if (!state.onChainAnalystResult) return 'onchain_analyst';
  if (!state.socialSentimentResult) return 'social_sentiment';
  if (!state.overallRiskScore) return 'risk_aggregator';
  
  return null; // All agents completed
};

// Risk aggregation logic
export const calculateOverallRisk = (state: TokenAnalysisState): {
  riskScore: number;
  rugPullProbability: number;
  alertLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  confidence: number;
} => {
  const weights = {
    contract: 0.4,    // Contract analysis is most important
    onchain: 0.3,     // On-chain metrics are crucial
    social: 0.2,      // Social signals matter
    token: 0.1,       // Basic token info
  };
  
  let totalRisk = 0;
  let totalConfidence = 0;
  let factorsCount = 0;
  
  // Contract risk (0-10 scale)
  if (state.contractAuditorResult) {
    totalRisk += state.contractAuditorResult.riskScore * weights.contract;
    totalConfidence += state.contractAuditorResult.confidence * weights.contract;
    factorsCount++;
  }
  
  // On-chain risk calculation
  if (state.onChainAnalystResult) {
    let onchainRisk = 0;
    
    // High holder concentration = higher risk
    if (state.onChainAnalystResult.holderConcentration > 0.8) onchainRisk += 3;
    else if (state.onChainAnalystResult.holderConcentration > 0.6) onchainRisk += 2;
    else if (state.onChainAnalystResult.holderConcentration > 0.4) onchainRisk += 1;
    
    // High creator balance = higher risk
    if (state.onChainAnalystResult.creatorBalance > 0.2) onchainRisk += 3;
    else if (state.onChainAnalystResult.creatorBalance > 0.1) onchainRisk += 2;
    
    // Low liquidity = higher risk
    if (state.onChainAnalystResult.liquidityUSD < 10000) onchainRisk += 2;
    else if (state.onChainAnalystResult.liquidityUSD < 50000) onchainRisk += 1;
    
    // Suspicious activity
    if (state.onChainAnalystResult.suspiciousActivity) onchainRisk += 2;
    
    totalRisk += Math.min(onchainRisk, 10) * weights.onchain;
    totalConfidence += 0.8 * weights.onchain; // High confidence in on-chain data
    factorsCount++;
  }
  
  // Social sentiment risk
  if (state.socialSentimentResult) {
    let socialRisk = 0;
    
    // Bot activity detected
    if (state.socialSentimentResult.botActivityDetected) socialRisk += 3;
    
    // Coordinated promotion
    if (state.socialSentimentResult.coordinatedPromotion) socialRisk += 2;
    
    // Very negative sentiment
    if (state.socialSentimentResult.sentimentScore < -0.5) socialRisk += 2;
    
    // Very low mentions (potential fake hype)
    if (state.socialSentimentResult.mentionCount < 10) socialRisk += 1;
    
    totalRisk += Math.min(socialRisk, 10) * weights.social;
    totalConfidence += 0.6 * weights.social; // Medium confidence in social data
    factorsCount++;
  }
  
  // Token basic risk
  if (state.tokenHunterResult) {
    let tokenRisk = 0;
    
    // Very new tokens are riskier
    if (state.tokenHunterResult.ageHours < 1) tokenRisk += 2;
    else if (state.tokenHunterResult.ageHours < 6) tokenRisk += 1;
    
    // Very low market cap
    if (state.tokenHunterResult.marketCap < 10000) tokenRisk += 2;
    
    totalRisk += Math.min(tokenRisk, 10) * weights.token;
    totalConfidence += 0.9 * weights.token; // High confidence in basic metrics
    factorsCount++;
  }
  
  const finalRiskScore = Math.min(totalRisk, 10);
  const rugPullProbability = finalRiskScore / 10;
  const finalConfidence = factorsCount > 0 ? totalConfidence / factorsCount : 0;
  
  // Determine alert level
  let alertLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  if (finalRiskScore >= 8) alertLevel = 'CRITICAL';
  else if (finalRiskScore >= 6) alertLevel = 'HIGH';
  else if (finalRiskScore >= 4) alertLevel = 'MEDIUM';
  else alertLevel = 'LOW';
  
  return {
    riskScore: finalRiskScore,
    rugPullProbability,
    alertLevel,
    confidence: finalConfidence,
  };
};

// Price direction prediction
export const predictPriceDirection = (state: TokenAnalysisState): 'UP' | 'DOWN' | 'SIDEWAYS' => {
  let bullishSignals = 0;
  let bearishSignals = 0;
  
  // Contract signals
  if (state.contractAuditorResult) {
    if (state.contractAuditorResult.riskScore < 3) bullishSignals++;
    if (state.contractAuditorResult.riskScore > 7) bearishSignals += 2;
    if (state.contractAuditorResult.liquidityLocked) bullishSignals++;
    if (state.contractAuditorResult.hiddenMintDetected) bearishSignals += 2;
  }
  
  // On-chain signals
  if (state.onChainAnalystResult) {
    if (state.onChainAnalystResult.liquidityChange24h > 0.2) bullishSignals++;
    if (state.onChainAnalystResult.liquidityChange24h < -0.2) bearishSignals++;
    if (state.onChainAnalystResult.holderConcentration < 0.4) bullishSignals++;
    if (state.onChainAnalystResult.creatorBalance > 0.2) bearishSignals++;
  }
  
  // Social signals
  if (state.socialSentimentResult) {
    if (state.socialSentimentResult.sentimentScore > 0.3) bullishSignals++;
    if (state.socialSentimentResult.sentimentScore < -0.3) bearishSignals++;
    if (state.socialSentimentResult.mentionCount > 100) bullishSignals++;
    if (state.socialSentimentResult.botActivityDetected) bearishSignals++;
  }
  
  if (bullishSignals > bearishSignals + 1) return 'UP';
  if (bearishSignals > bullishSignals + 1) return 'DOWN';
  return 'SIDEWAYS';
};
