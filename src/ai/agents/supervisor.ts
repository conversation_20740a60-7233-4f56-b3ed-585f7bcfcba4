/**
 * @fileOverview TokenSentinel Supervisor Agent - Orchestrates multi-agent rug detection workflow
 * Uses LangGraph pattern with OpenRouter DeepSeek R1 for cost-effective analysis
 */

import { z } from 'zod';

// Agent state schema for LangGraph workflow
export const TokenAnalysisState = z.object({
  tokenAddress: z.string(),
  tokenName: z.string().optional(),
  tokenSymbol: z.string().optional(),
  
  // Agent results
  tokenHunterResult: z.object({
    isNewToken: z.boolean(),
    ageHours: z.number(),
    marketCap: z.number(),
    volume24h: z.number(),
    source: z.string(), // 'pumpfun', 'dexscreener', etc.
  }).optional(),
  
  contractAuditorResult: z.object({
    riskScore: z.number().min(0).max(10),
    hiddenMintDetected: z.boolean(),
    ownershipRenounced: z.boolean(),
    liquidityLocked: z.boolean(),
    suspiciousFunctions: z.array(z.string()),
    confidence: z.number().min(0).max(1),
  }).optional(),
  
  onChainAnalystResult: z.object({
    holderConcentration: z.number(), // Top 10 holders percentage
    liquidityUSD: z.number(),
    liquidityChange24h: z.number(),
    largeTransactions: z.number(), // Count of >$10k transactions
    creatorBalance: z.number(), // Creator's token percentage
    suspiciousActivity: z.boolean(),
  }).optional(),
  
  socialSentimentResult: z.object({
    mentionCount: z.number(),
    sentimentScore: z.number().min(-1).max(1),
    botActivityDetected: z.boolean(),
    influencerMentions: z.number(),
    coordinatedPromotion: z.boolean(),
  }).optional(),
  
  // Final analysis
  overallRiskScore: z.number().min(0).max(10).optional(),
  rugPullProbability: z.number().min(0).max(1).optional(),
  priceDirection: z.enum(['UP', 'DOWN', 'SIDEWAYS']).optional(),
  confidence: z.number().min(0).max(1).optional(),
  alertLevel: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
  
  // Workflow control
  currentAgent: z.string().optional(),
  nextAgent: z.string().optional(),
  completed: z.boolean().default(false),
  errors: z.array(z.string()).default([]),

  // Data quality tracking
  dataQuality: z.number().min(0).max(1).optional(),
});

export type TokenAnalysisState = z.infer<typeof TokenAnalysisState>;

// Agent routing logic
export function determineNextAgent(state: TokenAnalysisState): string | null {
  // Sequential agent execution with error handling
  if (!state.tokenHunterResult) return 'token_hunter';
  if (!state.contractAuditorResult) return 'contract_auditor';
  if (!state.onChainAnalystResult) return 'onchain_analyst';
  if (!state.socialSentimentResult) return 'social_sentiment';
  if (!state.overallRiskScore) return 'risk_aggregator';
  
  return null; // All agents completed
}

// Risk aggregation logic
export function calculateOverallRisk(state: TokenAnalysisState): {
  riskScore: number;
  rugPullProbability: number;
  alertLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  confidence: number;
} {
  // Detect if this is a Solana token for appropriate weighting
  const isSolanaToken = state.tokenAddress.length === 44 && /^[1-9A-HJ-NP-Za-km-z]+$/.test(state.tokenAddress);

  const weights = isSolanaToken ? {
    // For Solana tokens, on-chain data is most important since contracts are standardized
    onchain: 0.4,     // On-chain metrics are most crucial for Solana
    token: 0.3,       // Token data quality and age matter more
    contract: 0.2,    // Contract analysis less critical (SPL tokens are standardized)
    social: 0.1,      // Social signals matter least
  } : {
    // For Ethereum tokens, contract analysis is most important
    contract: 0.4,    // Contract analysis is most important
    onchain: 0.3,     // On-chain metrics are crucial
    social: 0.2,      // Social signals matter
    token: 0.1,       // Basic token info
  };
  
  let totalRisk = 0;
  let totalConfidence = 0;
  let factorsCount = 0;
  
  // Contract risk (0-10 scale)
  if (state.contractAuditorResult) {
    totalRisk += state.contractAuditorResult.riskScore * weights.contract;
    totalConfidence += state.contractAuditorResult.confidence * weights.contract;
    factorsCount++;
  }
  
  // On-chain risk calculation
  if (state.onChainAnalystResult) {
    let onchainRisk = 0;
    let dataQuality = 0; // Track how much real data we have

    // High holder concentration = higher risk
    if (state.onChainAnalystResult.holderConcentration > 0) {
      dataQuality += 0.3;
      if (state.onChainAnalystResult.holderConcentration > 0.8) onchainRisk += 3;
      else if (state.onChainAnalystResult.holderConcentration > 0.6) onchainRisk += 2;
      else if (state.onChainAnalystResult.holderConcentration > 0.4) onchainRisk += 1;
    }

    // High creator balance = higher risk
    if (state.onChainAnalystResult.creatorBalance > 0) {
      dataQuality += 0.3;
      if (state.onChainAnalystResult.creatorBalance > 0.2) onchainRisk += 3;
      else if (state.onChainAnalystResult.creatorBalance > 0.1) onchainRisk += 2;
    }

    // Low liquidity = higher risk
    if (state.onChainAnalystResult.liquidityUSD > 0) {
      dataQuality += 0.3;
      if (state.onChainAnalystResult.liquidityUSD < 10000) onchainRisk += 2;
      else if (state.onChainAnalystResult.liquidityUSD < 50000) onchainRisk += 1;
    }

    // Suspicious activity
    if (state.onChainAnalystResult.suspiciousActivity) onchainRisk += 2;
    dataQuality += 0.1; // Always have suspicious activity check

    totalRisk += Math.min(onchainRisk, 10) * weights.onchain;
    totalConfidence += Math.max(0.3, dataQuality) * weights.onchain; // Confidence based on data quality
    factorsCount++;
  }
  
  // Social sentiment risk
  if (state.socialSentimentResult) {
    let socialRisk = 0;
    
    // Bot activity detected
    if (state.socialSentimentResult.botActivityDetected) socialRisk += 3;
    
    // Coordinated promotion
    if (state.socialSentimentResult.coordinatedPromotion) socialRisk += 2;
    
    // Very negative sentiment
    if (state.socialSentimentResult.sentimentScore < -0.5) socialRisk += 2;
    
    // Very low mentions (potential fake hype)
    if (state.socialSentimentResult.mentionCount < 10) socialRisk += 1;
    
    totalRisk += Math.min(socialRisk, 10) * weights.social;
    totalConfidence += 0.6 * weights.social; // Medium confidence in social data
    factorsCount++;
  }
  
  // Token basic risk
  if (state.tokenHunterResult) {
    let tokenRisk = 0;
    let tokenDataQuality = 0;

    // Age-based risk assessment
    if (state.tokenHunterResult.ageHours < 1) {
      tokenRisk += 3; // Very new tokens are high risk
    } else if (state.tokenHunterResult.ageHours < 6) {
      tokenRisk += 2; // New tokens are medium-high risk
    } else if (state.tokenHunterResult.ageHours < 24) {
      tokenRisk += 1; // Day-old tokens are medium risk
    }
    tokenDataQuality += 0.2; // Always have age data

    // Market cap based risk
    if (state.tokenHunterResult.marketCap > 0) {
      tokenDataQuality += 0.3;
      if (state.tokenHunterResult.marketCap < 1000) tokenRisk += 3;
      else if (state.tokenHunterResult.marketCap < 10000) tokenRisk += 2;
      else if (state.tokenHunterResult.marketCap < 100000) tokenRisk += 1;
    }

    // Volume analysis
    if (state.tokenHunterResult.volume24h > 0) {
      tokenDataQuality += 0.2;
      // Very low volume relative to market cap can indicate low interest
      if (state.tokenHunterResult.marketCap > 0) {
        const volumeRatio = state.tokenHunterResult.volume24h / state.tokenHunterResult.marketCap;
        if (volumeRatio < 0.01) tokenRisk += 1; // Very low volume
      }
    }

    // Data source quality bonus
    if (state.tokenHunterResult.source.includes('helius')) {
      tokenDataQuality += 0.3; // High quality blockchain data
    }
    if (state.tokenHunterResult.source.includes('dexscreener')) {
      tokenDataQuality += 0.2; // Good market data
    }

    totalRisk += Math.min(tokenRisk, 10) * weights.token;

    // Use enhanced data quality calculation
    const finalTokenDataQuality = Math.min(tokenDataQuality, 1.0);
    const dataQuality = state.dataQuality || finalTokenDataQuality; // Use multi-source confidence if available
    totalConfidence += Math.max(dataQuality, 0.3) * weights.token;
    factorsCount++;
  }
  
  const finalRiskScore = Math.min(totalRisk, 10);
  const rugPullProbability = finalRiskScore / 10;
  const finalConfidence = factorsCount > 0 ? totalConfidence / factorsCount : 0;
  
  // Determine alert level
  let alertLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  if (finalRiskScore >= 8) alertLevel = 'CRITICAL';
  else if (finalRiskScore >= 6) alertLevel = 'HIGH';
  else if (finalRiskScore >= 4) alertLevel = 'MEDIUM';
  else alertLevel = 'LOW';
  
  return {
    riskScore: finalRiskScore,
    rugPullProbability,
    alertLevel,
    confidence: finalConfidence,
  };
}

// Price direction prediction
export function predictPriceDirection(state: TokenAnalysisState): 'UP' | 'DOWN' | 'SIDEWAYS' {
  let bullishSignals = 0;
  let bearishSignals = 0;
  
  // Contract signals
  if (state.contractAuditorResult) {
    if (state.contractAuditorResult.riskScore < 3) bullishSignals++;
    if (state.contractAuditorResult.riskScore > 7) bearishSignals += 2;
    if (state.contractAuditorResult.liquidityLocked) bullishSignals++;
    if (state.contractAuditorResult.hiddenMintDetected) bearishSignals += 2;
  }
  
  // On-chain signals
  if (state.onChainAnalystResult) {
    if (state.onChainAnalystResult.liquidityChange24h > 0.2) bullishSignals++;
    if (state.onChainAnalystResult.liquidityChange24h < -0.2) bearishSignals++;
    if (state.onChainAnalystResult.holderConcentration < 0.4) bullishSignals++;
    if (state.onChainAnalystResult.creatorBalance > 0.2) bearishSignals++;
  }
  
  // Social signals
  if (state.socialSentimentResult) {
    if (state.socialSentimentResult.sentimentScore > 0.3) bullishSignals++;
    if (state.socialSentimentResult.sentimentScore < -0.3) bearishSignals++;
    if (state.socialSentimentResult.mentionCount > 100) bullishSignals++;
    if (state.socialSentimentResult.botActivityDetected) bearishSignals++;
  }
  
  if (bullishSignals > bearishSignals + 1) return 'UP';
  if (bearishSignals > bullishSignals + 1) return 'DOWN';
  return 'SIDEWAYS';
}
