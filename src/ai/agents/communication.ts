/**
 * @fileOverview Agent Communication Protocol for Multi-Agent Collaboration
 * Enables agents to communicate, challenge findings, and build consensus
 */

import { z } from 'zod';

// Agent types in the system
export type AgentType = 
  | 'token_hunter' 
  | 'contract_auditor' 
  | 'onchain_analyst' 
  | 'social_sentiment' 
  | 'risk_aggregator'
  | 'supervisor';

// Message types for agent communication
export type MessageType = 
  | 'REQUEST_DATA'      // Request specific data from another agent
  | 'PROVIDE_INSIGHT'   // Share analysis insights
  | 'CHALLENGE_FINDING' // Challenge another agent's conclusion
  | 'CONSENSUS'         // Propose consensus on findings
  | 'VALIDATION'        // Request validation of findings
  | 'CORRECTION';       // Provide correction to previous analysis

// Agent message schema
export const AgentMessage = z.object({
  id: z.string(),
  from: z.enum(['token_hunter', 'contract_auditor', 'onchain_analyst', 'social_sentiment', 'risk_aggregator', 'supervisor']),
  to: z.enum(['token_hunter', 'contract_auditor', 'onchain_analyst', 'social_sentiment', 'risk_aggregator', 'supervisor']),
  type: z.enum(['REQUEST_DATA', 'PROVIDE_INSIGHT', 'CHALLENGE_FINDING', 'CONSENSUS', 'VALIDATION', 'CORRECTION']),
  payload: z.any(),
  confidence: z.number().min(0).max(1),
  timestamp: z.number(),
  replyTo: z.string().optional(), // ID of message this is replying to
});

export type AgentMessage = z.infer<typeof AgentMessage>;

// Communication bus for agent messages
export class AgentCommunicationBus {
  private messages: AgentMessage[] = [];
  private listeners: Map<AgentType, ((message: AgentMessage) => void)[]> = new Map();
  private messageId = 0;

  constructor() {
    // Initialize listeners for each agent type
    const agentTypes: AgentType[] = ['token_hunter', 'contract_auditor', 'onchain_analyst', 'social_sentiment', 'risk_aggregator', 'supervisor'];
    agentTypes.forEach(type => {
      this.listeners.set(type, []);
    });
  }

  /**
   * Send a message from one agent to another
   */
  sendMessage(
    from: AgentType,
    to: AgentType,
    type: MessageType,
    payload: any,
    confidence: number,
    replyTo?: string
  ): string {
    const message: AgentMessage = {
      id: `msg_${++this.messageId}`,
      from,
      to,
      type,
      payload,
      confidence,
      timestamp: Date.now(),
      replyTo,
    };

    this.messages.push(message);
    
    // Notify listeners for the target agent
    const targetListeners = this.listeners.get(to) || [];
    targetListeners.forEach(listener => listener(message));

    console.log(`[AgentComm] ${from} -> ${to}: ${type} (confidence: ${confidence})`);
    return message.id;
  }

  /**
   * Subscribe to messages for a specific agent
   */
  subscribe(agentType: AgentType, listener: (message: AgentMessage) => void): void {
    const listeners = this.listeners.get(agentType) || [];
    listeners.push(listener);
    this.listeners.set(agentType, listeners);
  }

  /**
   * Get all messages in a conversation thread
   */
  getThread(messageId: string): AgentMessage[] {
    const thread: AgentMessage[] = [];
    const visited = new Set<string>();

    const findReplies = (id: string) => {
      if (visited.has(id)) return;
      visited.add(id);

      const message = this.messages.find(m => m.id === id);
      if (message) {
        thread.push(message);
        
        // Find all replies to this message
        const replies = this.messages.filter(m => m.replyTo === id);
        replies.forEach(reply => findReplies(reply.id));
      }
    };

    findReplies(messageId);
    return thread.sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * Get messages between two agents
   */
  getConversation(agent1: AgentType, agent2: AgentType): AgentMessage[] {
    return this.messages.filter(m => 
      (m.from === agent1 && m.to === agent2) || 
      (m.from === agent2 && m.to === agent1)
    ).sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * Clear all messages (for testing)
   */
  clear(): void {
    this.messages = [];
    this.messageId = 0;
  }

  /**
   * Get all messages for debugging
   */
  getAllMessages(): AgentMessage[] {
    return [...this.messages];
  }
}

// Singleton communication bus
export const agentCommunicationBus = new AgentCommunicationBus();

/**
 * Base class for collaborative agents
 */
export abstract class CollaborativeAgent {
  protected agentType: AgentType;
  protected communicationBus: AgentCommunicationBus;

  constructor(agentType: AgentType) {
    this.agentType = agentType;
    this.communicationBus = agentCommunicationBus;
    
    // Subscribe to messages for this agent
    this.communicationBus.subscribe(agentType, this.handleMessage.bind(this));
  }

  /**
   * Handle incoming messages from other agents
   */
  protected abstract handleMessage(message: AgentMessage): void;

  /**
   * Send a message to another agent
   */
  protected sendMessage(
    to: AgentType,
    type: MessageType,
    payload: any,
    confidence: number,
    replyTo?: string
  ): string {
    return this.communicationBus.sendMessage(
      this.agentType,
      to,
      type,
      payload,
      confidence,
      replyTo
    );
  }

  /**
   * Request data from another agent
   */
  protected requestData(to: AgentType, dataType: string, confidence: number = 0.8): string {
    return this.sendMessage(to, 'REQUEST_DATA', { dataType }, confidence);
  }

  /**
   * Challenge another agent's finding
   */
  protected challengeFinding(
    to: AgentType,
    finding: any,
    reason: string,
    confidence: number
  ): string {
    return this.sendMessage(to, 'CHALLENGE_FINDING', { finding, reason }, confidence);
  }

  /**
   * Provide insight to another agent
   */
  protected provideInsight(
    to: AgentType,
    insight: any,
    confidence: number
  ): string {
    return this.sendMessage(to, 'PROVIDE_INSIGHT', insight, confidence);
  }

  /**
   * Request validation from another agent
   */
  protected requestValidation(
    to: AgentType,
    finding: any,
    confidence: number
  ): string {
    return this.sendMessage(to, 'VALIDATION', finding, confidence);
  }
}

/**
 * Consensus building utilities
 */
export class ConsensusBuilder {
  private findings: Map<string, { agent: AgentType; value: any; confidence: number }[]> = new Map();

  /**
   * Add a finding from an agent
   */
  addFinding(key: string, agent: AgentType, value: any, confidence: number): void {
    if (!this.findings.has(key)) {
      this.findings.set(key, []);
    }
    
    const findings = this.findings.get(key)!;
    findings.push({ agent, value, confidence });
  }

  /**
   * Build consensus on a specific finding
   */
  buildConsensus(key: string): { value: any; confidence: number; agreement: number } | null {
    const findings = this.findings.get(key);
    if (!findings || findings.length === 0) {
      return null;
    }

    // Group by value
    const valueGroups = new Map<string, { agents: AgentType[]; totalConfidence: number; count: number }>();
    
    findings.forEach(({ agent, value, confidence }) => {
      const valueKey = JSON.stringify(value);
      if (!valueGroups.has(valueKey)) {
        valueGroups.set(valueKey, { agents: [], totalConfidence: 0, count: 0 });
      }
      
      const group = valueGroups.get(valueKey)!;
      group.agents.push(agent);
      group.totalConfidence += confidence;
      group.count++;
    });

    // Find the value with highest weighted consensus
    let bestValue: any = null;
    let bestScore = 0;
    let bestAgreement = 0;

    valueGroups.forEach((group, valueKey) => {
      const avgConfidence = group.totalConfidence / group.count;
      const agreement = group.count / findings.length;
      const score = avgConfidence * agreement;

      if (score > bestScore) {
        bestScore = score;
        bestValue = JSON.parse(valueKey);
        bestAgreement = agreement;
      }
    });

    return {
      value: bestValue,
      confidence: bestScore,
      agreement: bestAgreement,
    };
  }

  /**
   * Get all consensus results
   */
  getAllConsensus(): Map<string, { value: any; confidence: number; agreement: number }> {
    const results = new Map();
    
    this.findings.forEach((_, key) => {
      const consensus = this.buildConsensus(key);
      if (consensus) {
        results.set(key, consensus);
      }
    });

    return results;
  }

  /**
   * Clear all findings
   */
  clear(): void {
    this.findings.clear();
  }
}
