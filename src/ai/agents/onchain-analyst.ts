'use server';

/**
 * @fileOverview On-Chain Analyst Agent - Analyzes blockchain data for rug pull indicators
 * Monitors liquidity, holder distribution, transaction patterns, and wallet behavior
 */

import { z } from 'zod';
import type { TokenAnalysisState } from './supervisor';

const BIRDEYE_API_KEY = process.env.API_BIRDEYE_API_KEY;
const DUNE_API_KEY = process.env.DUNE_API_KEY;
const ETHERSCAN_API_KEY = process.env.ETHERSCAN_API_KEY;

// On-Chain Analyst result schema
export const OnChainAnalystResult = z.object({
  holderConcentration: z.number(),
  liquidityUSD: z.number(),
  liquidityChange24h: z.number(),
  largeTransactions: z.number(),
  creatorBalance: z.number(),
  suspiciousActivity: z.boolean(),
  topHolders: z.array(z.object({
    address: z.string(),
    balance: z.number(),
    percentage: z.number(),
  })),
  liquidityProviders: z.array(z.object({
    address: z.string(),
    amount: z.number(),
  })),
  unusualTransfers: z.array(z.object({
    hash: z.string(),
    from: z.string(),
    to: z.string(),
    amount: z.number(),
    timestamp: z.string(),
  })),
});

export type OnChainAnalystResult = z.infer<typeof OnChainAnalystResult>;

/**
 * On-Chain Analyst Agent - Analyzes blockchain metrics for rug pull indicators
 */
export async function executeOnChainAnalyst(
  state: TokenAnalysisState
): Promise<Partial<TokenAnalysisState>> {
  try {
    console.log(`[OnChainAnalyst] Analyzing on-chain data: ${state.tokenAddress}`);
    
    // Parallel data fetching for efficiency
    const [
      holderData,
      liquidityData,
      transactionData,
      creatorData,
    ] = await Promise.allSettled([
      fetchHolderDistribution(state.tokenAddress),
      fetchLiquidityMetrics(state.tokenAddress),
      fetchTransactionPatterns(state.tokenAddress),
      fetchCreatorBalance(state.tokenAddress),
    ]);
    
    // Process results with error handling
    const holders = holderData.status === 'fulfilled' ? holderData.value : null;
    const liquidity = liquidityData.status === 'fulfilled' ? liquidityData.value : null;
    const transactions = transactionData.status === 'fulfilled' ? transactionData.value : null;
    const creator = creatorData.status === 'fulfilled' ? creatorData.value : null;
    
    // Calculate risk metrics
    const analysis = calculateOnChainRisk({
      holders,
      liquidity,
      transactions,
      creator,
    });
    
    console.log(`[OnChainAnalyst] Holder concentration: ${analysis.holderConcentration}%`);
    console.log(`[OnChainAnalyst] Liquidity: $${analysis.liquidityUSD.toLocaleString()}`);
    
    return {
      ...state,
      onChainAnalystResult: analysis,
      currentAgent: 'onchain_analyst',
      nextAgent: 'social_sentiment',
    };
    
  } catch (error) {
    console.error('[OnChainAnalyst] Error:', error);
    return {
      ...state,
      errors: [...state.errors, `OnChainAnalyst failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
    };
  }
}

/**
 * Fetch holder distribution data
 */
async function fetchHolderDistribution(tokenAddress: string): Promise<{
  totalHolders: number;
  topHolders: Array<{
    address: string;
    balance: number;
    percentage: number;
  }>;
} | null> {
  if (!BIRDEYE_API_KEY) {
    console.warn('[OnChainAnalyst] Birdeye API key not configured');
    return null;
  }
  
  try {
    const url = `https://public-api.birdeye.so/defi/token_holders?address=${tokenAddress}&limit=50`;
    
    const response = await fetch(url, {
      headers: { 'X-API-KEY': BIRDEYE_API_KEY },
    });
    
    if (!response.ok) {
      throw new Error(`Birdeye holders API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data.success || !data.data) {
      return null;
    }
    
    const holders = data.data.items || [];
    const totalSupply = data.data.totalSupply || 1;
    
    return {
      totalHolders: data.data.total || holders.length,
      topHolders: holders.slice(0, 20).map((holder: any) => ({
        address: holder.address,
        balance: holder.balance,
        percentage: (holder.balance / totalSupply) * 100,
      })),
    };
    
  } catch (error) {
    console.error('[OnChainAnalyst] Holder distribution error:', error);
    return null;
  }
}

/**
 * Fetch liquidity metrics
 */
async function fetchLiquidityMetrics(tokenAddress: string): Promise<{
  currentLiquidity: number;
  liquidityChange24h: number;
  liquidityProviders: Array<{
    address: string;
    amount: number;
  }>;
} | null> {
  if (!BIRDEYE_API_KEY) {
    return null;
  }
  
  try {
    // Get current liquidity
    const overviewUrl = `https://public-api.birdeye.so/defi/token_overview?address=${tokenAddress}`;
    const overviewResponse = await fetch(overviewUrl, {
      headers: { 'X-API-KEY': BIRDEYE_API_KEY },
    });
    
    if (!overviewResponse.ok) {
      throw new Error(`Birdeye overview API error: ${overviewResponse.status}`);
    }
    
    const overviewData = await overviewResponse.json();
    const currentLiquidity = overviewData.data?.liquidity || 0;
    
    // Get historical liquidity (simplified - would need time-series data)
    const liquidityChange24h = 0; // Placeholder - would calculate from historical data
    
    return {
      currentLiquidity,
      liquidityChange24h,
      liquidityProviders: [], // Would fetch from DEX-specific APIs
    };
    
  } catch (error) {
    console.error('[OnChainAnalyst] Liquidity metrics error:', error);
    return null;
  }
}

/**
 * Fetch transaction patterns for suspicious activity detection
 */
async function fetchTransactionPatterns(tokenAddress: string): Promise<{
  largeTransactions: number;
  unusualTransfers: Array<{
    hash: string;
    from: string;
    to: string;
    amount: number;
    timestamp: string;
  }>;
  suspiciousActivity: boolean;
} | null> {
  // This would integrate with blockchain explorers or RPC nodes
  // For now, returning mock data structure
  
  try {
    // In a real implementation, this would:
    // 1. Fetch recent transactions from blockchain explorer
    // 2. Analyze transaction patterns
    // 3. Detect unusual activity (large sells, coordinated buys, etc.)
    
    return {
      largeTransactions: 0,
      unusualTransfers: [],
      suspiciousActivity: false,
    };
    
  } catch (error) {
    console.error('[OnChainAnalyst] Transaction patterns error:', error);
    return null;
  }
}

/**
 * Fetch creator/deployer balance and activity
 */
async function fetchCreatorBalance(tokenAddress: string): Promise<{
  creatorAddress: string;
  currentBalance: number;
  balancePercentage: number;
  recentActivity: boolean;
} | null> {
  // This would require:
  // 1. Identifying the token creator/deployer
  // 2. Checking their current token balance
  // 3. Monitoring their recent transactions
  
  try {
    // Placeholder implementation
    return {
      creatorAddress: '',
      currentBalance: 0,
      balancePercentage: 0,
      recentActivity: false,
    };
    
  } catch (error) {
    console.error('[OnChainAnalyst] Creator balance error:', error);
    return null;
  }
}

/**
 * Calculate on-chain risk metrics
 */
function calculateOnChainRisk(data: {
  holders: any;
  liquidity: any;
  transactions: any;
  creator: any;
}): OnChainAnalystResult {
  // Calculate holder concentration (top 10 holders percentage)
  let holderConcentration = 0;
  const topHolders: any[] = [];
  
  if (data.holders?.topHolders) {
    const top10 = data.holders.topHolders.slice(0, 10);
    holderConcentration = top10.reduce((sum: number, holder: any) => 
      sum + holder.percentage, 0
    );
    topHolders.push(...data.holders.topHolders);
  }
  
  // Liquidity metrics
  const liquidityUSD = data.liquidity?.currentLiquidity || 0;
  const liquidityChange24h = data.liquidity?.liquidityChange24h || 0;
  
  // Transaction analysis
  const largeTransactions = data.transactions?.largeTransactions || 0;
  const unusualTransfers = data.transactions?.unusualTransfers || [];
  
  // Creator balance
  const creatorBalance = data.creator?.balancePercentage || 0;
  
  // Detect suspicious activity
  let suspiciousActivity = false;
  
  // High holder concentration is suspicious
  if (holderConcentration > 80) suspiciousActivity = true;
  
  // High creator balance is suspicious
  if (creatorBalance > 20) suspiciousActivity = true;
  
  // Large liquidity decrease is suspicious
  if (liquidityChange24h < -0.5) suspiciousActivity = true;
  
  // Many large transactions could indicate dumping
  if (largeTransactions > 10) suspiciousActivity = true;
  
  return {
    holderConcentration: holderConcentration / 100, // Convert to decimal
    liquidityUSD,
    liquidityChange24h,
    largeTransactions,
    creatorBalance: creatorBalance / 100, // Convert to decimal
    suspiciousActivity,
    topHolders,
    liquidityProviders: data.liquidity?.liquidityProviders || [],
    unusualTransfers,
  };
}

/**
 * Monitor real-time liquidity changes
 */
export async function monitorLiquidityChanges(
  tokenAddress: string,
  callback: (change: { address: string; liquidityUSD: number; change: number }) => void
): Promise<void> {
  // This would set up real-time monitoring of liquidity pools
  // and call the callback when significant changes are detected
  
  console.log(`[OnChainAnalyst] Starting liquidity monitoring for ${tokenAddress}`);
  
  // Implementation would use WebSocket connections to DEX APIs
  // or blockchain event listeners
}

/**
 * Detect wash trading patterns
 */
export function detectWashTrading(transactions: any[]): {
  suspiciousPatterns: boolean;
  confidence: number;
  details: string[];
} {
  // Analyze transaction patterns for wash trading indicators:
  // 1. Same addresses buying and selling repeatedly
  // 2. Round-number transactions
  // 3. Coordinated timing
  // 4. Artificial volume inflation
  
  return {
    suspiciousPatterns: false,
    confidence: 0,
    details: [],
  };
}
