/**
 * @fileOverview Social Sentiment Agent - Analyzes social media for token sentiment and manipulation
 * Monitors Twitter/X, Reddit, Telegram for coordinated promotion, bot activity, and sentiment
 */

import { z } from 'zod';
import type { TokenAnalysisState } from './supervisor';
import { createOpenRouterClient } from '../genkit';

const REDDIT_CLIENT_ID = process.env.REDDIT_CLIENT_ID;
const REDDIT_CLIENT_SECRET = process.env.REDDIT_CLIENT_SECRET;
const SCRAPINGBEE_API_KEY = process.env.SCRAPINGBEE_API_KEY;

// Social Sentiment result schema
export const SocialSentimentResult = z.object({
  mentionCount: z.number(),
  sentimentScore: z.number().min(-1).max(1),
  botActivityDetected: z.boolean(),
  influencerMentions: z.number(),
  coordinatedPromotion: z.boolean(),
  platforms: z.object({
    twitter: z.object({
      mentions: z.number(),
      sentiment: z.number(),
      engagement: z.number(),
    }).optional(),
    reddit: z.object({
      mentions: z.number(),
      sentiment: z.number(),
      upvotes: z.number(),
    }).optional(),
    telegram: z.object({
      mentions: z.number(),
      sentiment: z.number(),
      channels: z.number(),
    }).optional(),
  }),
  suspiciousPatterns: z.array(z.string()),
  keyMentions: z.array(z.object({
    platform: z.string(),
    content: z.string(),
    author: z.string(),
    timestamp: z.string(),
    engagement: z.number(),
  })),
});

export type SocialSentimentResult = z.infer<typeof SocialSentimentResult>;

/**
 * Social Sentiment Agent - Analyzes social media sentiment and manipulation
 */
export async function executeSocialSentiment(
  state: TokenAnalysisState
): Promise<Partial<TokenAnalysisState>> {
  try {
    console.log(`[SocialSentiment] Analyzing social sentiment: ${state.tokenName || state.tokenAddress}`);
    
    const searchTerms = [
      state.tokenName,
      state.tokenSymbol,
      state.tokenAddress,
    ].filter(Boolean);
    
    // Parallel social media analysis
    const [
      twitterData,
      redditData,
      telegramData,
    ] = await Promise.allSettled([
      analyzeTwitterSentiment(searchTerms),
      analyzeRedditSentiment(searchTerms),
      analyzeTelegramSentiment(searchTerms),
    ]);
    
    // Process results
    const twitter = twitterData.status === 'fulfilled' ? twitterData.value : null;
    const reddit = redditData.status === 'fulfilled' ? redditData.value : null;
    const telegram = telegramData.status === 'fulfilled' ? telegramData.value : null;
    
    // Aggregate sentiment analysis
    const analysis = aggregateSocialAnalysis({
      twitter,
      reddit,
      telegram,
    });
    
    // Detect manipulation patterns
    const manipulation = detectManipulationPatterns(analysis);
    
    const result: SocialSentimentResult = {
      ...analysis,
      ...manipulation,
    };
    
    console.log(`[SocialSentiment] Overall sentiment: ${result.sentimentScore.toFixed(2)}`);
    console.log(`[SocialSentiment] Bot activity detected: ${result.botActivityDetected}`);
    
    return {
      ...state,
      socialSentimentResult: result,
      currentAgent: 'social_sentiment',
      nextAgent: 'risk_aggregator',
    };
    
  } catch (error) {
    console.error('[SocialSentiment] Error:', error);
    return {
      ...state,
      errors: [...state.errors, `SocialSentiment failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
    };
  }
}

/**
 * Analyze Twitter/X sentiment using web scraping
 */
async function analyzeTwitterSentiment(searchTerms: string[]): Promise<{
  mentions: number;
  sentiment: number;
  engagement: number;
  posts: Array<{
    content: string;
    author: string;
    timestamp: string;
    engagement: number;
  }>;
} | null> {
  if (!SCRAPINGBEE_API_KEY) {
    console.warn('[SocialSentiment] ScrapingBee API key not configured');
    return null;
  }
  
  try {
    // Use ScrapingBee to scrape Twitter search results
    const searchQuery = searchTerms.join(' OR ');
    const url = `https://twitter.com/search?q=${encodeURIComponent(searchQuery)}&src=typed_query&f=live`;
    
    const response = await fetch(`https://app.scrapingbee.com/api/v1/?api_key=${SCRAPINGBEE_API_KEY}&url=${encodeURIComponent(url)}&render_js=true`);
    
    if (!response.ok) {
      throw new Error(`ScrapingBee API error: ${response.status}`);
    }
    
    const html = await response.text();
    
    // Parse Twitter data (simplified - would need robust HTML parsing)
    const posts = parseTwitterHTML(html);
    
    // Analyze sentiment using AI
    const sentimentAnalysis = await analyzeSentimentWithAI(
      posts.map(p => p.content),
      'twitter'
    );
    
    return {
      mentions: posts.length,
      sentiment: sentimentAnalysis.averageSentiment,
      engagement: posts.reduce((sum, post) => sum + post.engagement, 0),
      posts,
    };
    
  } catch (error) {
    console.error('[SocialSentiment] Twitter analysis error:', error);
    return null;
  }
}

/**
 * Analyze Reddit sentiment
 */
async function analyzeRedditSentiment(searchTerms: string[]): Promise<{
  mentions: number;
  sentiment: number;
  upvotes: number;
  posts: Array<{
    content: string;
    author: string;
    timestamp: string;
    upvotes: number;
  }>;
} | null> {
  if (!REDDIT_CLIENT_ID || !REDDIT_CLIENT_SECRET) {
    console.warn('[SocialSentiment] Reddit API credentials not configured');
    return null;
  }
  
  try {
    // Get Reddit access token
    const tokenResponse = await fetch('https://www.reddit.com/api/v1/access_token', {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(`${REDDIT_CLIENT_ID}:${REDDIT_CLIENT_SECRET}`).toString('base64')}`,
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'TokenSentinel/1.0',
      },
      body: 'grant_type=client_credentials',
    });
    
    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;
    
    // Search Reddit for mentions
    const searchQuery = searchTerms.join(' OR ');
    const searchResponse = await fetch(
      `https://oauth.reddit.com/search?q=${encodeURIComponent(searchQuery)}&sort=new&limit=50`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'User-Agent': 'TokenSentinel/1.0',
        },
      }
    );

    if (!searchResponse.ok) {
      console.log(`[SocialSentiment] Reddit search API ${searchResponse.status}, skipping social analysis`);
      return null;
    }

    const responseText = await searchResponse.text();
    let searchData;

    try {
      searchData = JSON.parse(responseText);
    } catch (error) {
      console.log('[SocialSentiment] Reddit API returned non-JSON response, skipping social analysis');
      return null;
    }
    const posts = searchData.data?.children || [];
    
    // Process Reddit posts
    const processedPosts = posts.map((post: any) => ({
      content: post.data.title + ' ' + (post.data.selftext || ''),
      author: post.data.author,
      timestamp: new Date(post.data.created_utc * 1000).toISOString(),
      upvotes: post.data.ups,
    }));
    
    // Analyze sentiment
    const sentimentAnalysis = await analyzeSentimentWithAI(
      processedPosts.map(p => p.content),
      'reddit'
    );
    
    return {
      mentions: processedPosts.length,
      sentiment: sentimentAnalysis.averageSentiment,
      upvotes: processedPosts.reduce((sum, post) => sum + post.upvotes, 0),
      posts: processedPosts,
    };
    
  } catch (error) {
    console.error('[SocialSentiment] Reddit analysis error:', error);
    return null;
  }
}

/**
 * Analyze Telegram sentiment (simplified implementation)
 */
async function analyzeTelegramSentiment(searchTerms: string[]): Promise<{
  mentions: number;
  sentiment: number;
  channels: number;
} | null> {
  // Telegram analysis would require:
  // 1. Access to Telegram Bot API or web scraping
  // 2. Monitoring specific crypto channels
  // 3. Real-time message analysis
  
  // For now, returning null (not implemented)
  return null;
}

/**
 * AI-powered sentiment analysis using DeepSeek R1
 */
async function analyzeSentimentWithAI(
  texts: string[],
  platform: string
): Promise<{
  averageSentiment: number;
  sentiments: number[];
  botLikelihood: number;
}> {
  if (texts.length === 0) {
    return { averageSentiment: 0, sentiments: [], botLikelihood: 0 };
  }
  
  try {
    const openRouter = createOpenRouterClient();
    
    const prompt = `Analyze the sentiment of these ${platform} posts about a cryptocurrency token. 
    
Posts:
${texts.slice(0, 20).map((text, i) => `${i + 1}. ${text.substring(0, 200)}`).join('\n')}

For each post, provide:
1. Sentiment score (-1 to 1, where -1 is very negative, 0 is neutral, 1 is very positive)
2. Bot likelihood (0 to 1, where 1 means very likely to be a bot)

Also detect patterns that suggest coordinated promotion or manipulation.

Response format:
{
  "sentiments": [score1, score2, ...],
  "botLikelihoods": [likelihood1, likelihood2, ...],
  "coordinatedPromotion": boolean,
  "manipulationPatterns": ["pattern1", "pattern2"]
}`;

    const response = await fetch(openRouter.baseURL + '/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouter.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: openRouter.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1,
        max_tokens: 1500,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }
    
    const data = await response.json();
    const content = data.choices[0]?.message?.content;
    
    if (!content) {
      throw new Error('No response from AI sentiment analysis');
    }
    
    try {
      const parsed = JSON.parse(content);
      const sentiments = parsed.sentiments || [];
      const botLikelihoods = parsed.botLikelihoods || [];
      
      const averageSentiment = sentiments.length > 0 ? 
        sentiments.reduce((sum: number, s: number) => sum + s, 0) / sentiments.length : 0;
      
      const averageBotLikelihood = botLikelihoods.length > 0 ?
        botLikelihoods.reduce((sum: number, b: number) => sum + b, 0) / botLikelihoods.length : 0;
      
      return {
        averageSentiment,
        sentiments,
        botLikelihood: averageBotLikelihood,
      };
      
    } catch (parseError) {
      console.error('[SocialSentiment] Failed to parse AI response:', parseError);
      return { averageSentiment: 0, sentiments: [], botLikelihood: 0 };
    }
    
  } catch (error) {
    console.error('[SocialSentiment] AI sentiment analysis error:', error);
    return { averageSentiment: 0, sentiments: [], botLikelihood: 0 };
  }
}

/**
 * Parse Twitter HTML (simplified implementation)
 */
function parseTwitterHTML(html: string): Array<{
  content: string;
  author: string;
  timestamp: string;
  engagement: number;
}> {
  // This would require robust HTML parsing
  // For now, returning empty array
  return [];
}

/**
 * Aggregate social media analysis results
 */
function aggregateSocialAnalysis(data: {
  twitter: any;
  reddit: any;
  telegram: any;
}): Omit<SocialSentimentResult, 'botActivityDetected' | 'coordinatedPromotion' | 'suspiciousPatterns'> {
  let totalMentions = 0;
  let weightedSentiment = 0;
  let totalWeight = 0;
  let influencerMentions = 0;
  
  const platforms: any = {};
  const keyMentions: any[] = [];
  
  // Process Twitter data
  if (data.twitter) {
    totalMentions += data.twitter.mentions;
    weightedSentiment += data.twitter.sentiment * data.twitter.mentions;
    totalWeight += data.twitter.mentions;
    
    platforms.twitter = {
      mentions: data.twitter.mentions,
      sentiment: data.twitter.sentiment,
      engagement: data.twitter.engagement,
    };
    
    // Add key Twitter mentions
    keyMentions.push(...data.twitter.posts.slice(0, 5).map((post: any) => ({
      platform: 'twitter',
      content: post.content,
      author: post.author,
      timestamp: post.timestamp,
      engagement: post.engagement,
    })));
  }
  
  // Process Reddit data
  if (data.reddit) {
    totalMentions += data.reddit.mentions;
    weightedSentiment += data.reddit.sentiment * data.reddit.mentions;
    totalWeight += data.reddit.mentions;
    
    platforms.reddit = {
      mentions: data.reddit.mentions,
      sentiment: data.reddit.sentiment,
      upvotes: data.reddit.upvotes,
    };
    
    // Add key Reddit mentions
    keyMentions.push(...data.reddit.posts.slice(0, 5).map((post: any) => ({
      platform: 'reddit',
      content: post.content,
      author: post.author,
      timestamp: post.timestamp,
      engagement: post.upvotes,
    })));
  }
  
  // Process Telegram data
  if (data.telegram) {
    totalMentions += data.telegram.mentions;
    weightedSentiment += data.telegram.sentiment * data.telegram.mentions;
    totalWeight += data.telegram.mentions;
    
    platforms.telegram = {
      mentions: data.telegram.mentions,
      sentiment: data.telegram.sentiment,
      channels: data.telegram.channels,
    };
  }
  
  const averageSentiment = totalWeight > 0 ? weightedSentiment / totalWeight : 0;
  
  return {
    mentionCount: totalMentions,
    sentimentScore: averageSentiment,
    influencerMentions,
    platforms,
    keyMentions,
  };
}

/**
 * Detect manipulation patterns in social media data
 */
function detectManipulationPatterns(analysis: any): {
  botActivityDetected: boolean;
  coordinatedPromotion: boolean;
  suspiciousPatterns: string[];
} {
  const suspiciousPatterns: string[] = [];
  let botActivityDetected = false;
  let coordinatedPromotion = false;
  
  // Check for bot activity indicators
  // (This would be more sophisticated in a real implementation)
  
  // Low mention count with high engagement could indicate bots
  if (analysis.mentionCount < 10 && analysis.platforms.twitter?.engagement > 1000) {
    suspiciousPatterns.push('Low mentions but high engagement (possible bot activity)');
    botActivityDetected = true;
  }
  
  // Very positive sentiment with low mentions could indicate coordinated promotion
  if (analysis.sentimentScore > 0.8 && analysis.mentionCount < 50) {
    suspiciousPatterns.push('Unusually positive sentiment with low mention count');
    coordinatedPromotion = true;
  }
  
  return {
    botActivityDetected,
    coordinatedPromotion,
    suspiciousPatterns,
  };
}
