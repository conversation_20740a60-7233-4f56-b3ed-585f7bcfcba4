'use server';

/**
 * @fileOverview Token Hunter Agent - Detects and validates new token launches
 * Monitors PumpFun WebSocket, DexScreener API, and other sources for tokens <24h old
 */

import { z } from 'zod';
import type { TokenAnalysisState } from './supervisor';

const DEXSCREENER_API_BASE = 'https://api.dexscreener.com/latest/dex';
const BIRDEYE_API_BASE = 'https://public-api.birdeye.so/defi';

// Token Hunter result schema
export const TokenHunterResult = z.object({
  isNewToken: z.boolean(),
  ageHours: z.number(),
  marketCap: z.number(),
  volume24h: z.number(),
  source: z.string(),
  launchTime: z.string(),
  dexPair: z.string().optional(),
  initialLiquidity: z.number().optional(),
  priceUSD: z.number().optional(),
});

export type TokenHunterResult = z.infer<typeof TokenHunterResult>;

/**
 * Token Hunter Agent - Primary function to detect and validate new tokens
 */
export async function executeTokenHunter(
  state: TokenAnalysisState
): Promise<Partial<TokenAnalysisState>> {
  try {
    console.log(`[TokenHunter] Analyzing token: ${state.tokenAddress}`);
    
    // Multi-source token validation
    const [birdeyeData, dexScreenerData] = await Promise.allSettled([
      fetchBirdeyeTokenData(state.tokenAddress),
      fetchDexScreenerData(state.tokenAddress),
    ]);
    
    let tokenData: any = null;
    let source = 'unknown';
    
    // Prioritize Birdeye data for Solana tokens
    if (birdeyeData.status === 'fulfilled' && birdeyeData.value) {
      tokenData = birdeyeData.value;
      source = 'birdeye';
    } else if (dexScreenerData.status === 'fulfilled' && dexScreenerData.value) {
      tokenData = dexScreenerData.value;
      source = 'dexscreener';
    }
    
    if (!tokenData) {
      return {
        ...state,
        tokenHunterResult: {
          isNewToken: false,
          ageHours: 0,
          marketCap: 0,
          volume24h: 0,
          source: 'none',
        },
        errors: [...state.errors, 'Token not found in any data source'],
      };
    }
    
    // Calculate token age
    const launchTime = tokenData.createdAt || tokenData.pairCreatedAt || new Date().toISOString();
    const ageHours = calculateTokenAge(launchTime);
    
    // Determine if token is "new" (< 24 hours)
    const isNewToken = ageHours < 24;
    
    const result: TokenHunterResult = {
      isNewToken,
      ageHours,
      marketCap: tokenData.marketCap || tokenData.mc || 0,
      volume24h: tokenData.volume24h || tokenData.v24h || 0,
      source,
      launchTime,
      dexPair: tokenData.pairAddress,
      initialLiquidity: tokenData.liquidity,
      priceUSD: tokenData.price || tokenData.priceUsd,
    };
    
    console.log(`[TokenHunter] Result:`, result);
    
    return {
      ...state,
      tokenHunterResult: result,
      tokenName: tokenData.name || state.tokenName,
      tokenSymbol: tokenData.symbol || state.tokenSymbol,
      currentAgent: 'token_hunter',
      nextAgent: 'contract_auditor',
    };
    
  } catch (error) {
    console.error('[TokenHunter] Error:', error);
    return {
      ...state,
      errors: [...state.errors, `TokenHunter failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
    };
  }
}

/**
 * Fetch token data from Birdeye API (Solana-focused)
 */
async function fetchBirdeyeTokenData(tokenAddress: string): Promise<any> {
  const BIRDEYE_API_KEY = process.env.API_BIRDEYE_API_KEY;
  
  if (!BIRDEYE_API_KEY) {
    throw new Error('Birdeye API key not configured');
  }
  
  const url = `${BIRDEYE_API_BASE}/token_overview?address=${tokenAddress}`;
  
  try {
    const response = await fetch(url, {
      headers: { 'X-API-KEY': BIRDEYE_API_KEY },
    });
    
    if (!response.ok) {
      throw new Error(`Birdeye API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data.success || !data.data) {
      return null;
    }
    
    return {
      name: data.data.name,
      symbol: data.data.symbol,
      price: data.data.price,
      marketCap: data.data.mc,
      volume24h: data.data.v24h,
      liquidity: data.data.liquidity,
      createdAt: data.data.createdAt,
      holders: data.data.holders,
    };
    
  } catch (error) {
    console.error('[TokenHunter] Birdeye API error:', error);
    return null;
  }
}

/**
 * Fetch token data from DexScreener API (Multi-chain)
 */
async function fetchDexScreenerData(tokenAddress: string): Promise<any> {
  const url = `${DEXSCREENER_API_BASE}/tokens/${tokenAddress}`;
  
  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`DexScreener API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data.pairs || data.pairs.length === 0) {
      return null;
    }
    
    // Get the most liquid pair
    const mainPair = data.pairs.reduce((prev: any, current: any) => 
      (current.liquidity?.usd || 0) > (prev.liquidity?.usd || 0) ? current : prev
    );
    
    return {
      name: mainPair.baseToken?.name,
      symbol: mainPair.baseToken?.symbol,
      price: parseFloat(mainPair.priceUsd || '0'),
      marketCap: mainPair.marketCap,
      volume24h: mainPair.volume?.h24 || 0,
      liquidity: mainPair.liquidity?.usd || 0,
      pairCreatedAt: mainPair.pairCreatedAt,
      pairAddress: mainPair.pairAddress,
      dexId: mainPair.dexId,
    };
    
  } catch (error) {
    console.error('[TokenHunter] DexScreener API error:', error);
    return null;
  }
}

/**
 * Calculate token age in hours from launch time
 */
function calculateTokenAge(launchTime: string): number {
  try {
    const launch = new Date(launchTime);
    const now = new Date();
    const diffMs = now.getTime() - launch.getTime();
    return diffMs / (1000 * 60 * 60); // Convert to hours
  } catch (error) {
    console.error('[TokenHunter] Error calculating age:', error);
    return 0;
  }
}

/**
 * Validate token address format (basic validation)
 */
export function isValidTokenAddress(address: string): boolean {
  // Solana address validation (base58, 32-44 characters)
  const solanaRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
  
  // Ethereum address validation (hex, 42 characters)
  const ethereumRegex = /^0x[a-fA-F0-9]{40}$/;
  
  return solanaRegex.test(address) || ethereumRegex.test(address);
}

/**
 * Filter tokens by criteria (market cap, volume, age)
 */
export function shouldAnalyzeToken(result: TokenHunterResult): boolean {
  // Only analyze new tokens
  if (!result.isNewToken) return false;
  
  // Minimum market cap threshold ($1,000)
  if (result.marketCap < 1000) return false;
  
  // Minimum volume threshold ($100)
  if (result.volume24h < 100) return false;
  
  // Skip tokens older than 24 hours
  if (result.ageHours > 24) return false;
  
  return true;
}

/**
 * Real-time token monitoring from PumpFun WebSocket
 * This would integrate with your existing WebSocket service
 */
export async function monitorNewTokens(
  callback: (tokenAddress: string) => void
): Promise<void> {
  // This would integrate with your existing WebSocket implementation
  // in src/hooks/use-websocket.tsx and src/lib/websocket-service.ts
  
  console.log('[TokenHunter] Starting real-time token monitoring...');
  
  // Implementation would connect to PumpPortal WebSocket
  // and call callback for each new token detected
}
