/**
 * @fileOverview Price Forecaster Agent - Advanced price direction prediction
 * Uses technical analysis, on-chain metrics, social sentiment, and ML patterns
 */

import { z } from 'zod';
import type { TokenAnalysisState } from './supervisor';
import { createOpenRouterClient } from '../genkit';

// Price forecast result schema
export const PriceForecastResult = z.object({
  direction: z.enum(['UP', 'DOWN', 'SIDEWAYS']),
  confidence: z.number().min(0).max(1),
  timeframe: z.enum(['1H', '4H', '24H', '7D']),
  priceTargets: z.object({
    bullish: z.number().optional(),
    bearish: z.number().optional(),
    support: z.number().optional(),
    resistance: z.number().optional(),
  }),
  signals: z.object({
    technical: z.array(z.string()),
    onchain: z.array(z.string()),
    social: z.array(z.string()),
    risk: z.array(z.string()),
  }),
  momentum: z.number().min(-1).max(1), // -1 = strong bearish, +1 = strong bullish
  volatility: z.enum(['LOW', 'MEDIUM', 'HIGH', 'EXTREME']),
});

export type PriceForecastResult = z.infer<typeof PriceForecastResult>;

/**
 * Advanced price forecasting using multi-factor analysis
 */
export async function executePriceForecaster(
  state: TokenAnalysisState
): Promise<PriceForecastResult> {
  try {
    console.log(`[PriceForecaster] Analyzing price direction for ${state.tokenName}`);
    
    // Gather all available signals
    const technicalSignals = analyzeTechnicalSignals(state);
    const onchainSignals = analyzeOnChainSignals(state);
    const socialSignals = analyzeSocialSignals(state);
    const riskSignals = analyzeRiskSignals(state);
    
    // Use AI for advanced pattern recognition
    const aiAnalysis = await performAIPriceAnalysis(state, {
      technical: technicalSignals,
      onchain: onchainSignals,
      social: socialSignals,
      risk: riskSignals,
    });
    
    // Combine all signals for final prediction
    const forecast = combineSignalsForForecast({
      technical: technicalSignals,
      onchain: onchainSignals,
      social: socialSignals,
      risk: riskSignals,
      ai: aiAnalysis,
    });
    
    console.log(`[PriceForecaster] Prediction: ${forecast.direction} (${(forecast.confidence * 100).toFixed(0)}% confidence)`);
    
    return forecast;
    
  } catch (error) {
    console.error('[PriceForecaster] Error:', error);
    
    // Return neutral forecast on error
    return {
      direction: 'SIDEWAYS',
      confidence: 0.1,
      timeframe: '24H',
      priceTargets: {},
      signals: {
        technical: [`Error: ${error instanceof Error ? error.message : 'Unknown error'}`],
        onchain: [],
        social: [],
        risk: [],
      },
      momentum: 0,
      volatility: 'HIGH',
    };
  }
}

/**
 * Analyze technical indicators and patterns
 */
function analyzeTechnicalSignals(state: TokenAnalysisState): {
  signals: string[];
  bullishScore: number;
  bearishScore: number;
} {
  const signals: string[] = [];
  let bullishScore = 0;
  let bearishScore = 0;
  
  if (state.tokenHunterResult) {
    const token = state.tokenHunterResult;
    
    // Volume analysis
    if (token.volume24h > token.marketCap * 0.1) {
      signals.push('High volume relative to market cap');
      bullishScore += 2;
    } else if (token.volume24h < token.marketCap * 0.01) {
      signals.push('Low volume - potential liquidity issues');
      bearishScore += 1;
    }
    
    // Market cap analysis
    if (token.marketCap < 100000) {
      signals.push('Micro-cap token - high volatility expected');
      bearishScore += 1;
    } else if (token.marketCap > 10000000) {
      signals.push('Established market cap - more stability');
      bullishScore += 1;
    }
    
    // Age analysis
    if (token.ageHours < 1) {
      signals.push('Very new token - extreme volatility risk');
      bearishScore += 2;
    } else if (token.ageHours > 168) { // 1 week
      signals.push('Token survived initial volatility period');
      bullishScore += 1;
    }
  }
  
  return { signals, bullishScore, bearishScore };
}

/**
 * Analyze on-chain metrics for price signals
 */
function analyzeOnChainSignals(state: TokenAnalysisState): {
  signals: string[];
  bullishScore: number;
  bearishScore: number;
} {
  const signals: string[] = [];
  let bullishScore = 0;
  let bearishScore = 0;
  
  if (state.onChainAnalystResult) {
    const onchain = state.onChainAnalystResult;
    
    // Liquidity analysis
    if (onchain.liquidityUSD > 100000) {
      signals.push('Strong liquidity supports price stability');
      bullishScore += 2;
    } else if (onchain.liquidityUSD < 10000) {
      signals.push('Low liquidity - high slippage risk');
      bearishScore += 2;
    }
    
    // Liquidity change analysis
    if (onchain.liquidityChange24h > 0.2) {
      signals.push('Liquidity increasing - positive momentum');
      bullishScore += 2;
    } else if (onchain.liquidityChange24h < -0.2) {
      signals.push('Liquidity decreasing - potential exit');
      bearishScore += 3;
    }
    
    // Holder concentration analysis
    if (onchain.holderConcentration < 0.3) {
      signals.push('Well-distributed holdings - healthy ecosystem');
      bullishScore += 2;
    } else if (onchain.holderConcentration > 0.8) {
      signals.push('High concentration - manipulation risk');
      bearishScore += 3;
    }
    
    // Creator balance analysis
    if (onchain.creatorBalance < 0.05) {
      signals.push('Low creator balance - reduced dump risk');
      bullishScore += 1;
    } else if (onchain.creatorBalance > 0.2) {
      signals.push('High creator balance - dump risk');
      bearishScore += 2;
    }
    
    // Large transactions analysis
    if (onchain.largeTransactions > 5) {
      signals.push('High whale activity - increased volatility');
      bearishScore += 1;
    }
    
    // Suspicious activity
    if (onchain.suspiciousActivity) {
      signals.push('Suspicious on-chain activity detected');
      bearishScore += 3;
    }
  }
  
  return { signals, bullishScore, bearishScore };
}

/**
 * Analyze social sentiment for price signals
 */
function analyzeSocialSignals(state: TokenAnalysisState): {
  signals: string[];
  bullishScore: number;
  bearishScore: number;
} {
  const signals: string[] = [];
  let bullishScore = 0;
  let bearishScore = 0;
  
  if (state.socialSentimentResult) {
    const social = state.socialSentimentResult;
    
    // Mention count analysis
    if (social.mentionCount > 100) {
      signals.push('High social media attention');
      bullishScore += 2;
    } else if (social.mentionCount < 10) {
      signals.push('Low social media presence');
      bearishScore += 1;
    }
    
    // Sentiment analysis
    if (social.sentimentScore > 0.5) {
      signals.push('Positive social sentiment');
      bullishScore += 2;
    } else if (social.sentimentScore < -0.3) {
      signals.push('Negative social sentiment');
      bearishScore += 2;
    }
    
    // Bot activity detection
    if (social.botActivityDetected) {
      signals.push('Bot activity detected - artificial hype');
      bearishScore += 3;
    }
    
    // Coordinated promotion
    if (social.coordinatedPromotion) {
      signals.push('Coordinated promotion campaign');
      bearishScore += 2;
    }
    
    // Influencer mentions
    if (social.influencerMentions > 5) {
      signals.push('Multiple influencer mentions');
      bullishScore += 1;
    }
  }
  
  return { signals, bullishScore, bearishScore };
}

/**
 * Analyze risk factors for price impact
 */
function analyzeRiskSignals(state: TokenAnalysisState): {
  signals: string[];
  bullishScore: number;
  bearishScore: number;
} {
  const signals: string[] = [];
  let bullishScore = 0;
  let bearishScore = 0;
  
  if (state.contractAuditorResult) {
    const contract = state.contractAuditorResult;
    
    // Contract verification
    if (contract.contractVerified) {
      signals.push('Contract verified - transparency positive');
      bullishScore += 1;
    } else {
      signals.push('Unverified contract - transparency risk');
      bearishScore += 1;
    }
    
    // Hidden mint detection
    if (contract.hiddenMintDetected) {
      signals.push('Hidden mint function - extreme dump risk');
      bearishScore += 5;
    }
    
    // Ownership analysis
    if (contract.ownershipRenounced) {
      signals.push('Ownership renounced - reduced centralization');
      bullishScore += 2;
    } else {
      signals.push('Ownership not renounced - centralization risk');
      bearishScore += 1;
    }
    
    // Blacklist function
    if (contract.blacklistFunction) {
      signals.push('Blacklist function present - trading restriction risk');
      bearishScore += 2;
    }
    
    // Pausable contract
    if (contract.pausable) {
      signals.push('Contract pausable - trading halt risk');
      bearishScore += 1;
    }
  }
  
  // Overall risk score impact
  if (state.overallRiskScore) {
    if (state.overallRiskScore > 8) {
      signals.push('Critical risk level - avoid completely');
      bearishScore += 5;
    } else if (state.overallRiskScore > 6) {
      signals.push('High risk level - extreme caution');
      bearishScore += 3;
    } else if (state.overallRiskScore < 3) {
      signals.push('Low risk level - relatively safe');
      bullishScore += 2;
    }
  }
  
  return { signals, bullishScore, bearishScore };
}

/**
 * AI-powered price analysis using DeepSeek R1
 */
async function performAIPriceAnalysis(
  state: TokenAnalysisState,
  signals: any
): Promise<{
  direction: 'UP' | 'DOWN' | 'SIDEWAYS';
  confidence: number;
  reasoning: string;
  momentum: number;
}> {
  try {
    const openRouter = createOpenRouterClient();
    
    const prompt = `You are a senior cryptocurrency analyst specializing in price prediction for new tokens. Analyze this token's data and predict price direction.

Token: ${state.tokenName} (${state.tokenSymbol})
Address: ${state.tokenAddress}

Technical Signals: ${JSON.stringify(signals.technical, null, 2)}
On-Chain Signals: ${JSON.stringify(signals.onchain, null, 2)}
Social Signals: ${JSON.stringify(signals.social, null, 2)}
Risk Signals: ${JSON.stringify(signals.risk, null, 2)}

Based on this comprehensive data, predict:
1. Price direction (UP/DOWN/SIDEWAYS) for next 24 hours
2. Confidence level (0-1)
3. Momentum score (-1 to +1, where -1 is strong bearish, +1 is strong bullish)
4. Brief reasoning

Consider:
- New tokens are highly volatile
- Risk factors heavily impact price sustainability
- Social sentiment can drive short-term moves
- On-chain metrics indicate real demand/supply

Response format:
{
  "direction": "UP|DOWN|SIDEWAYS",
  "confidence": 0.0-1.0,
  "momentum": -1.0 to 1.0,
  "reasoning": "Brief explanation"
}`;

    const response = await fetch(openRouter.baseURL + '/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouter.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: openRouter.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.2, // Low temperature for consistent analysis
        max_tokens: 800,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }
    
    const data = await response.json();
    const content = data.choices[0]?.message?.content;
    
    if (!content) {
      throw new Error('No response from AI analysis');
    }
    
    try {
      const parsed = JSON.parse(content);
      return {
        direction: parsed.direction || 'SIDEWAYS',
        confidence: Math.max(0, Math.min(1, parsed.confidence || 0.5)),
        reasoning: parsed.reasoning || 'AI analysis completed',
        momentum: Math.max(-1, Math.min(1, parsed.momentum || 0)),
      };
    } catch (parseError) {
      console.error('[PriceForecaster] Failed to parse AI response:', parseError);
      return {
        direction: 'SIDEWAYS',
        confidence: 0.3,
        reasoning: 'AI analysis parsing failed',
        momentum: 0,
      };
    }
    
  } catch (error) {
    console.error('[PriceForecaster] AI analysis error:', error);
    return {
      direction: 'SIDEWAYS',
      confidence: 0.2,
      reasoning: 'AI analysis unavailable',
      momentum: 0,
    };
  }
}

/**
 * Combine all signals for final forecast
 */
function combineSignalsForForecast(data: {
  technical: any;
  onchain: any;
  social: any;
  risk: any;
  ai: any;
}): PriceForecastResult {
  // Calculate weighted scores
  const weights = {
    technical: 0.2,
    onchain: 0.3,
    social: 0.2,
    risk: 0.2,
    ai: 0.1,
  };
  
  let totalBullishScore = 0;
  let totalBearishScore = 0;
  
  // Weight the scores
  totalBullishScore += data.technical.bullishScore * weights.technical;
  totalBearishScore += data.technical.bearishScore * weights.technical;
  
  totalBullishScore += data.onchain.bullishScore * weights.onchain;
  totalBearishScore += data.onchain.bearishScore * weights.onchain;
  
  totalBullishScore += data.social.bullishScore * weights.social;
  totalBearishScore += data.social.bearishScore * weights.social;
  
  totalBullishScore += data.risk.bullishScore * weights.risk;
  totalBearishScore += data.risk.bearishScore * weights.risk;
  
  // AI influence
  if (data.ai.direction === 'UP') {
    totalBullishScore += data.ai.confidence * weights.ai * 10;
  } else if (data.ai.direction === 'DOWN') {
    totalBearishScore += data.ai.confidence * weights.ai * 10;
  }
  
  // Determine direction
  let direction: 'UP' | 'DOWN' | 'SIDEWAYS';
  let confidence: number;
  
  const scoreDifference = Math.abs(totalBullishScore - totalBearishScore);
  const totalScore = totalBullishScore + totalBearishScore;
  
  if (scoreDifference < 1 || totalScore < 2) {
    direction = 'SIDEWAYS';
    confidence = 0.3 + (scoreDifference / 10);
  } else if (totalBullishScore > totalBearishScore) {
    direction = 'UP';
    confidence = Math.min(0.9, 0.5 + (scoreDifference / 10));
  } else {
    direction = 'DOWN';
    confidence = Math.min(0.9, 0.5 + (scoreDifference / 10));
  }
  
  // Calculate momentum
  const momentum = totalScore > 0 ? 
    (totalBullishScore - totalBearishScore) / Math.max(totalScore, 1) : 0;
  
  // Determine volatility
  let volatility: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME';
  if (totalScore > 8) volatility = 'EXTREME';
  else if (totalScore > 5) volatility = 'HIGH';
  else if (totalScore > 2) volatility = 'MEDIUM';
  else volatility = 'LOW';
  
  return {
    direction,
    confidence: Math.max(0.1, Math.min(0.95, confidence)),
    timeframe: '24H',
    priceTargets: {}, // Would calculate based on technical analysis
    signals: {
      technical: data.technical.signals,
      onchain: data.onchain.signals,
      social: data.social.signals,
      risk: data.risk.signals,
    },
    momentum: Math.max(-1, Math.min(1, momentum)),
    volatility,
  };
}
