/**
 * @fileOverview Contract Auditor Agent - Analyzes smart contracts for rug pull indicators
 * Uses static analysis, pattern matching, and heuristics to detect malicious code
 */

import { z } from 'zod';
import type { TokenAnalysisState } from './supervisor';
import { createOpenRouterClient } from '../genkit';

const ETHERSCAN_API_KEY = process.env.ETHERSCAN_API_KEY;
const ETHERSCAN_API_BASE = 'https://api.etherscan.io/api';

// Contract Auditor result schema
export const ContractAuditorResult = z.object({
  riskScore: z.number().min(0).max(10),
  hiddenMintDetected: z.boolean(),
  ownershipRenounced: z.boolean(),
  liquidityLocked: z.boolean(),
  suspiciousFunctions: z.array(z.string()),
  confidence: z.number().min(0).max(1),
  contractVerified: z.boolean(),
  proxyContract: z.boolean(),
  pausable: z.boolean(),
  blacklistFunction: z.boolean(),
  maxTransactionLimit: z.boolean(),
});

export type ContractAuditorResult = z.infer<typeof ContractAuditorResult>;

// Known rug pull patterns and signatures
const RUG_PULL_PATTERNS = {
  HIDDEN_MINT: [
    'function mint(',
    'function _mint(',
    'function mintTo(',
    'function increaseSupply(',
    'function addSupply(',
  ],
  OWNERSHIP_FUNCTIONS: [
    'function transferOwnership(',
    'function renounceOwnership(',
    'onlyOwner',
    'Ownable',
  ],
  BLACKLIST_FUNCTIONS: [
    'function blacklist(',
    'function addToBlacklist(',
    'function setBlacklist(',
    'mapping(address => bool) blacklisted',
    'mapping(address => bool) blocked',
  ],
  PAUSE_FUNCTIONS: [
    'function pause(',
    'function unpause(',
    'whenNotPaused',
    'Pausable',
  ],
  PROXY_PATTERNS: [
    'delegatecall',
    'Proxy',
    'Implementation',
    'upgrade',
  ],
  SUSPICIOUS_MODIFIERS: [
    'onlyDev',
    'onlyTeam',
    'onlyCreator',
    'restricted',
  ],
};

/**
 * Contract Auditor Agent - Analyzes smart contract for rug pull indicators
 */
export async function executeContractAuditor(
  state: TokenAnalysisState
): Promise<Partial<TokenAnalysisState>> {
  try {
    console.log(`[ContractAuditor] Analyzing contract: ${state.tokenAddress}`);
    
    // Fetch contract source code
    const contractData = await fetchContractSource(state.tokenAddress);
    
    if (!contractData) {
      return {
        ...state,
        contractAuditorResult: {
          riskScore: 5, // Medium risk for unverified contracts
          hiddenMintDetected: false,
          ownershipRenounced: false,
          liquidityLocked: false,
          suspiciousFunctions: [],
          confidence: 0.3, // Low confidence without source code
          contractVerified: false,
          proxyContract: false,
          pausable: false,
          blacklistFunction: false,
          maxTransactionLimit: false,
        },
        errors: [...state.errors, 'Contract source code not available'],
      };
    }
    
    // Analyze contract code
    const analysis = await analyzeContractCode(contractData.sourceCode);
    
    // Use DeepSeek R1 for advanced pattern analysis
    const aiAnalysis = await performAIContractAnalysis(
      contractData.sourceCode,
      state.tokenAddress
    );
    
    // Combine static analysis with AI insights
    const result = combineAnalysisResults(analysis, aiAnalysis, contractData);
    
    console.log(`[ContractAuditor] Risk Score: ${result.riskScore}/10`);
    
    return {
      ...state,
      contractAuditorResult: result,
      currentAgent: 'contract_auditor',
      nextAgent: 'onchain_analyst',
    };
    
  } catch (error) {
    console.error('[ContractAuditor] Error:', error);
    return {
      ...state,
      errors: [...state.errors, `ContractAuditor failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
    };
  }
}

/**
 * Fetch contract source code from Etherscan
 */
async function fetchContractSource(contractAddress: string): Promise<{
  sourceCode: string;
  contractName: string;
  compilerVersion: string;
  isProxy: boolean;
} | null> {
  if (!ETHERSCAN_API_KEY) {
    console.warn('[ContractAuditor] Etherscan API key not configured');
    return null;
  }
  
  try {
    const url = `${ETHERSCAN_API_BASE}?module=contract&action=getsourcecode&address=${contractAddress}&apikey=${ETHERSCAN_API_KEY}`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.status !== '1' || !data.result || !data.result[0]) {
      return null;
    }
    
    const result = data.result[0];
    
    if (!result.SourceCode || result.SourceCode === '') {
      return null;
    }
    
    return {
      sourceCode: result.SourceCode,
      contractName: result.ContractName,
      compilerVersion: result.CompilerVersion,
      isProxy: result.Proxy === '1',
    };
    
  } catch (error) {
    console.error('[ContractAuditor] Etherscan API error:', error);
    return null;
  }
}

/**
 * Static analysis of contract code for rug pull patterns
 */
async function analyzeContractCode(sourceCode: string): Promise<{
  hiddenMintDetected: boolean;
  ownershipRenounced: boolean;
  suspiciousFunctions: string[];
  pausable: boolean;
  blacklistFunction: boolean;
  maxTransactionLimit: boolean;
}> {
  const suspiciousFunctions: string[] = [];
  let hiddenMintDetected = false;
  let ownershipRenounced = false;
  let pausable = false;
  let blacklistFunction = false;
  let maxTransactionLimit = false;
  
  // Check for hidden mint functions
  for (const pattern of RUG_PULL_PATTERNS.HIDDEN_MINT) {
    if (sourceCode.includes(pattern)) {
      hiddenMintDetected = true;
      suspiciousFunctions.push(`Hidden mint function: ${pattern}`);
    }
  }
  
  // Check for ownership patterns
  const hasOwnership = RUG_PULL_PATTERNS.OWNERSHIP_FUNCTIONS.some(pattern => 
    sourceCode.includes(pattern)
  );
  
  // Check if ownership is renounced
  ownershipRenounced = sourceCode.includes('renounceOwnership()') || 
                      sourceCode.includes('owner = address(0)');
  
  // Check for blacklist functions
  for (const pattern of RUG_PULL_PATTERNS.BLACKLIST_FUNCTIONS) {
    if (sourceCode.includes(pattern)) {
      blacklistFunction = true;
      suspiciousFunctions.push(`Blacklist function: ${pattern}`);
    }
  }
  
  // Check for pause functions
  for (const pattern of RUG_PULL_PATTERNS.PAUSE_FUNCTIONS) {
    if (sourceCode.includes(pattern)) {
      pausable = true;
      suspiciousFunctions.push(`Pause function: ${pattern}`);
    }
  }
  
  // Check for transaction limits
  if (sourceCode.includes('maxTransactionAmount') || 
      sourceCode.includes('_maxTxAmount') ||
      sourceCode.includes('maxTx')) {
    maxTransactionLimit = true;
    suspiciousFunctions.push('Max transaction limit detected');
  }
  
  // Check for suspicious modifiers
  for (const pattern of RUG_PULL_PATTERNS.SUSPICIOUS_MODIFIERS) {
    if (sourceCode.includes(pattern)) {
      suspiciousFunctions.push(`Suspicious modifier: ${pattern}`);
    }
  }
  
  return {
    hiddenMintDetected,
    ownershipRenounced,
    suspiciousFunctions,
    pausable,
    blacklistFunction,
    maxTransactionLimit,
  };
}

/**
 * AI-powered contract analysis using DeepSeek R1
 */
async function performAIContractAnalysis(
  sourceCode: string,
  contractAddress: string
): Promise<{
  riskAssessment: string;
  additionalFindings: string[];
  confidence: number;
}> {
  try {
    const openRouter = createOpenRouterClient();
    
    const prompt = `You are a senior smart contract auditor specializing in rug pull detection. Analyze this Solana/Ethereum token contract for potential rug pull indicators.

Contract Address: ${contractAddress}

Source Code:
${sourceCode.substring(0, 8000)} // Truncate for API limits

Focus on:
1. Hidden mint functions or backdoors
2. Ownership patterns and centralization risks
3. Liquidity manipulation possibilities
4. Unusual transfer restrictions
5. Proxy patterns that could hide malicious code

Provide a risk assessment (1-10 scale) and list specific concerns.

Response format:
{
  "riskScore": <number 1-10>,
  "findings": ["finding1", "finding2"],
  "confidence": <number 0-1>
}`;

    const response = await fetch(openRouter.baseURL + '/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouter.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: openRouter.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1, // Low temperature for consistent analysis
        max_tokens: 1000,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }
    
    const data = await response.json();
    const content = data.choices[0]?.message?.content;
    
    if (!content) {
      throw new Error('No response from AI analysis');
    }
    
    // Parse AI response
    try {
      const parsed = JSON.parse(content);
      return {
        riskAssessment: `AI Risk Score: ${parsed.riskScore}/10`,
        additionalFindings: parsed.findings || [],
        confidence: parsed.confidence || 0.7,
      };
    } catch (parseError) {
      // Fallback if JSON parsing fails
      return {
        riskAssessment: content,
        additionalFindings: [],
        confidence: 0.5,
      };
    }
    
  } catch (error) {
    console.error('[ContractAuditor] AI analysis error:', error);
    return {
      riskAssessment: 'AI analysis unavailable',
      additionalFindings: [],
      confidence: 0.3,
    };
  }
}

/**
 * Combine static analysis with AI insights
 */
function combineAnalysisResults(
  staticAnalysis: any,
  aiAnalysis: any,
  contractData: any
): ContractAuditorResult {
  let riskScore = 0;
  
  // Base risk factors
  if (staticAnalysis.hiddenMintDetected) riskScore += 3;
  if (!staticAnalysis.ownershipRenounced) riskScore += 2;
  if (staticAnalysis.blacklistFunction) riskScore += 2;
  if (staticAnalysis.pausable) riskScore += 1;
  if (staticAnalysis.maxTransactionLimit) riskScore += 1;
  if (contractData.isProxy) riskScore += 1;
  if (!contractData.sourceCode) riskScore += 2; // Unverified contract
  
  // AI risk adjustment
  if (aiAnalysis.riskAssessment.includes('high risk')) riskScore += 1;
  if (aiAnalysis.riskAssessment.includes('low risk')) riskScore -= 1;
  
  // Ensure risk score is within bounds
  riskScore = Math.max(0, Math.min(10, riskScore));
  
  // Calculate confidence
  const confidence = contractData.sourceCode ? 
    Math.min(0.9, 0.6 + (aiAnalysis.confidence * 0.3)) : 0.3;
  
  return {
    riskScore,
    hiddenMintDetected: staticAnalysis.hiddenMintDetected,
    ownershipRenounced: staticAnalysis.ownershipRenounced,
    liquidityLocked: false, // This would require additional on-chain analysis
    suspiciousFunctions: [
      ...staticAnalysis.suspiciousFunctions,
      ...aiAnalysis.additionalFindings,
    ],
    confidence,
    contractVerified: !!contractData.sourceCode,
    proxyContract: contractData.isProxy,
    pausable: staticAnalysis.pausable,
    blacklistFunction: staticAnalysis.blacklistFunction,
    maxTransactionLimit: staticAnalysis.maxTransactionLimit,
  };
}
