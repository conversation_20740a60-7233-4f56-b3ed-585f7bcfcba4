/**
 * @fileOverview Contract Auditor Agent - Analyzes smart contracts for rug pull indicators
 * Uses static analysis, pattern matching, and heuristics to detect malicious code
 * Enhanced with collaborative agent communication and consensus building
 */

import { z } from 'zod';
import type { TokenAnalysisState } from './supervisor';
import { createOpenRouterClient } from '../genkit';
import { CollaborativeAgent, AgentMessage, agentCommunicationBus, ConsensusBuilder } from './communication';

const ETHERSCAN_API_KEY = process.env.ETHERSCAN_API_KEY;
const ETHERSCAN_API_BASE = 'https://api.etherscan.io/api';

// Contract Auditor result schema
export const ContractAuditorResult = z.object({
  riskScore: z.number().min(0).max(10),
  hiddenMintDetected: z.boolean(),
  ownershipRenounced: z.boolean(),
  liquidityLocked: z.boolean(),
  suspiciousFunctions: z.array(z.string()),
  confidence: z.number().min(0).max(1),
  contractVerified: z.boolean(),
  proxyContract: z.boolean(),
  pausable: z.boolean(),
  blacklistFunction: z.boolean(),
  maxTransactionLimit: z.boolean(),
  aiReasoning: z.string().optional(), // DeepSeek AI reasoning for transparency
});

export type ContractAuditorResult = z.infer<typeof ContractAuditorResult>;

/**
 * Solana-specific token analysis using DeepSeek R1 AI reasoning
 * Since Solana tokens don't have traditional smart contracts like Ethereum,
 * we analyze based on token program type and on-chain characteristics
 */
async function analyzeSolanaToken(state: TokenAnalysisState): Promise<Partial<TokenAnalysisState>> {
  try {
    console.log(`[ContractAuditor] Starting DeepSeek AI analysis for Solana token`);

    // Use DeepSeek R1 for intelligent Solana token analysis
    const aiAnalysis = await performSolanaAIAnalysis(state);

    // Combine AI insights with basic Solana token characteristics
    const baseAnalysis = await performBasicSolanaAnalysis(state);

    // Merge AI analysis with base analysis
    const finalRiskScore = Math.round((aiAnalysis.riskScore + baseAnalysis.riskScore) / 2);
    const finalConfidence = Math.max(aiAnalysis.confidence, baseAnalysis.confidence);
    const combinedFindings = [...aiAnalysis.findings, ...baseAnalysis.suspiciousFunctions];

    console.log(`[ContractAuditor] Solana AI analysis - Risk Score: ${finalRiskScore}/10, Confidence: ${finalConfidence}`);
    console.log(`[ContractAuditor] AI Reasoning: ${aiAnalysis.reasoning}`);

    return {
      ...state,
      contractAuditorResult: {
        riskScore: finalRiskScore,
        hiddenMintDetected: false, // SPL tokens don't have hidden mint functions
        ownershipRenounced: true, // Most SPL tokens have no ongoing admin control
        liquidityLocked: false, // Would need additional analysis
        suspiciousFunctions: combinedFindings,
        confidence: finalConfidence,
        contractVerified: true, // SPL Token program is verified
        proxyContract: false, // SPL tokens are not proxy contracts
        pausable: false, // Standard SPL tokens cannot be paused
        blacklistFunction: false, // Standard SPL tokens don't have blacklist
        maxTransactionLimit: false, // Standard SPL tokens don't have tx limits
        aiReasoning: aiAnalysis.reasoning, // Store AI reasoning for transparency
      },
    };

  } catch (error) {
    console.error('[ContractAuditor] Solana AI analysis error:', error);

    // Fallback to basic analysis if AI fails
    const fallbackAnalysis = await performBasicSolanaAnalysis(state);

    return {
      ...state,
      contractAuditorResult: {
        riskScore: fallbackAnalysis.riskScore,
        hiddenMintDetected: false,
        ownershipRenounced: true,
        liquidityLocked: false,
        suspiciousFunctions: fallbackAnalysis.suspiciousFunctions,
        confidence: fallbackAnalysis.confidence,
        contractVerified: true,
        proxyContract: false,
        pausable: false,
        blacklistFunction: false,
        maxTransactionLimit: false,
      },
      errors: [...state.errors, `Solana AI analysis failed, using fallback: ${error instanceof Error ? error.message : 'Unknown error'}`],
    };
  }
}

// Known rug pull patterns and signatures
const RUG_PULL_PATTERNS = {
  HIDDEN_MINT: [
    'function mint(',
    'function _mint(',
    'function mintTo(',
    'function increaseSupply(',
    'function addSupply(',
  ],
  OWNERSHIP_FUNCTIONS: [
    'function transferOwnership(',
    'function renounceOwnership(',
    'onlyOwner',
    'Ownable',
  ],
  BLACKLIST_FUNCTIONS: [
    'function blacklist(',
    'function addToBlacklist(',
    'function setBlacklist(',
    'mapping(address => bool) blacklisted',
    'mapping(address => bool) blocked',
  ],
  PAUSE_FUNCTIONS: [
    'function pause(',
    'function unpause(',
    'whenNotPaused',
    'Pausable',
  ],
  PROXY_PATTERNS: [
    'delegatecall',
    'Proxy',
    'Implementation',
    'upgrade',
  ],
  SUSPICIOUS_MODIFIERS: [
    'onlyDev',
    'onlyTeam',
    'onlyCreator',
    'restricted',
  ],
};

/**
 * Contract Auditor Agent - Analyzes smart contract for rug pull indicators
 */
export async function executeContractAuditor(
  state: TokenAnalysisState
): Promise<Partial<TokenAnalysisState>> {
  try {
    console.log(`[ContractAuditor] Analyzing contract: ${state.tokenAddress}`);

    // Detect if this is a Solana token (44 characters, base58)
    const isSolanaToken = state.tokenAddress.length === 44 && /^[1-9A-HJ-NP-Za-km-z]+$/.test(state.tokenAddress);

    if (isSolanaToken) {
      console.log(`[ContractAuditor] Detected Solana token - using Solana-specific analysis`);
      return await analyzeSolanaToken(state);
    }

    // Fetch contract source code for Ethereum tokens
    const contractData = await fetchContractSource(state.tokenAddress);

    if (!contractData) {
      return {
        ...state,
        contractAuditorResult: {
          riskScore: 5, // Medium risk for unverified contracts
          hiddenMintDetected: false,
          ownershipRenounced: false,
          liquidityLocked: false,
          suspiciousFunctions: [],
          confidence: 0.3, // Low confidence without source code
          contractVerified: false,
          proxyContract: false,
          pausable: false,
          blacklistFunction: false,
          maxTransactionLimit: false,
        },
        errors: [...state.errors, 'Contract source code not available'],
      };
    }
    
    // Analyze contract code
    const analysis = await analyzeContractCode(contractData.sourceCode);
    
    // Use DeepSeek R1 for advanced pattern analysis
    const aiAnalysis = await performAIContractAnalysis(
      contractData.sourceCode,
      state.tokenAddress
    );
    
    // Combine static analysis with AI insights
    const result = combineAnalysisResults(analysis, aiAnalysis, contractData);
    
    console.log(`[ContractAuditor] Risk Score: ${result.riskScore}/10`);
    
    return {
      ...state,
      contractAuditorResult: result,
      currentAgent: 'contract_auditor',
      nextAgent: 'onchain_analyst',
    };
    
  } catch (error) {
    console.error('[ContractAuditor] Error:', error);
    return {
      ...state,
      errors: [...state.errors, `ContractAuditor failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
    };
  }
}

/**
 * Fetch contract source code from Etherscan
 */
async function fetchContractSource(contractAddress: string): Promise<{
  sourceCode: string;
  contractName: string;
  compilerVersion: string;
  isProxy: boolean;
} | null> {
  if (!ETHERSCAN_API_KEY) {
    console.warn('[ContractAuditor] Etherscan API key not configured');
    return null;
  }
  
  try {
    const url = `${ETHERSCAN_API_BASE}?module=contract&action=getsourcecode&address=${contractAddress}&apikey=${ETHERSCAN_API_KEY}`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.status !== '1' || !data.result || !data.result[0]) {
      return null;
    }
    
    const result = data.result[0];
    
    if (!result.SourceCode || result.SourceCode === '') {
      return null;
    }
    
    return {
      sourceCode: result.SourceCode,
      contractName: result.ContractName,
      compilerVersion: result.CompilerVersion,
      isProxy: result.Proxy === '1',
    };
    
  } catch (error) {
    console.error('[ContractAuditor] Etherscan API error:', error);
    return null;
  }
}

/**
 * Static analysis of contract code for rug pull patterns
 */
async function analyzeContractCode(sourceCode: string): Promise<{
  hiddenMintDetected: boolean;
  ownershipRenounced: boolean;
  suspiciousFunctions: string[];
  pausable: boolean;
  blacklistFunction: boolean;
  maxTransactionLimit: boolean;
}> {
  const suspiciousFunctions: string[] = [];
  let hiddenMintDetected = false;
  let ownershipRenounced = false;
  let pausable = false;
  let blacklistFunction = false;
  let maxTransactionLimit = false;
  
  // Check for hidden mint functions
  for (const pattern of RUG_PULL_PATTERNS.HIDDEN_MINT) {
    if (sourceCode.includes(pattern)) {
      hiddenMintDetected = true;
      suspiciousFunctions.push(`Hidden mint function: ${pattern}`);
    }
  }
  
  // Check for ownership patterns
  const hasOwnership = RUG_PULL_PATTERNS.OWNERSHIP_FUNCTIONS.some(pattern => 
    sourceCode.includes(pattern)
  );
  
  // Check if ownership is renounced
  ownershipRenounced = sourceCode.includes('renounceOwnership()') || 
                      sourceCode.includes('owner = address(0)');
  
  // Check for blacklist functions
  for (const pattern of RUG_PULL_PATTERNS.BLACKLIST_FUNCTIONS) {
    if (sourceCode.includes(pattern)) {
      blacklistFunction = true;
      suspiciousFunctions.push(`Blacklist function: ${pattern}`);
    }
  }
  
  // Check for pause functions
  for (const pattern of RUG_PULL_PATTERNS.PAUSE_FUNCTIONS) {
    if (sourceCode.includes(pattern)) {
      pausable = true;
      suspiciousFunctions.push(`Pause function: ${pattern}`);
    }
  }
  
  // Check for transaction limits
  if (sourceCode.includes('maxTransactionAmount') || 
      sourceCode.includes('_maxTxAmount') ||
      sourceCode.includes('maxTx')) {
    maxTransactionLimit = true;
    suspiciousFunctions.push('Max transaction limit detected');
  }
  
  // Check for suspicious modifiers
  for (const pattern of RUG_PULL_PATTERNS.SUSPICIOUS_MODIFIERS) {
    if (sourceCode.includes(pattern)) {
      suspiciousFunctions.push(`Suspicious modifier: ${pattern}`);
    }
  }
  
  return {
    hiddenMintDetected,
    ownershipRenounced,
    suspiciousFunctions,
    pausable,
    blacklistFunction,
    maxTransactionLimit,
  };
}

/**
 * DeepSeek R1-0528 AI analysis specifically for Solana tokens with validation
 */
async function performSolanaAIAnalysis(state: TokenAnalysisState): Promise<{
  riskScore: number;
  confidence: number;
  reasoning: string;
  findings: string[];
}> {
  try {
    const openRouter = createOpenRouterClient();

    // Prepare comprehensive token data for AI analysis
    const tokenData = {
      address: state.tokenAddress,
      name: state.tokenName || 'Unknown',
      symbol: state.tokenSymbol || 'Unknown',
      age: state.tokenHunterResult?.ageHours || 0,
      marketCap: state.tokenHunterResult?.marketCap || 0,
      volume24h: state.tokenHunterResult?.volume24h || 0,
      liquidity: state.tokenHunterResult?.initialLiquidity || 0,
      price: state.tokenHunterResult?.priceUSD || 0,
      dataSource: state.tokenHunterResult?.source || 'unknown',
      holderData: state.onChainAnalystResult?.holderConcentration || null,
      topHolders: state.onChainAnalystResult?.topHolders?.length || 0,
    };

    console.log(`[ContractAuditor] Using DeepSeek R1T2 Chimera model for analysis`);

    const prompt = `You are a senior cryptocurrency analyst specializing in Solana token rug pull detection. Analyze this Solana SPL token for potential risks and legitimacy.

CRITICAL CONTEXT: This is a Solana SPL token, NOT an Ethereum contract. Solana tokens use the standardized SPL Token program, so traditional contract vulnerabilities don't apply. Focus on economic and behavioral indicators.

TOKEN DATA:
- Address: ${tokenData.address}
- Name: ${tokenData.name} (${tokenData.symbol})
- Age: ${tokenData.age} hours old
- Market Cap: $${tokenData.marketCap.toLocaleString()}
- 24h Volume: $${tokenData.volume24h.toLocaleString()}
- Liquidity: $${tokenData.liquidity.toLocaleString()}
- Price: $${tokenData.price}
- Data Sources: ${tokenData.dataSource}
- Holder Concentration: ${tokenData.holderData ? (tokenData.holderData * 100).toFixed(2) + '%' : 'Unknown'}
- Top Holders Analyzed: ${tokenData.topHolders}

ANALYSIS FRAMEWORK FOR SOLANA TOKENS:
1. **Age Risk**: Very new tokens (<1h) are extremely high risk, <24h are high risk
2. **Market Cap Risk**: <$10k very high risk, <$100k medium risk, >$1M lower risk
3. **Volume/Liquidity Ratio**: Healthy tokens have volume 0.1-5x liquidity daily
4. **Holder Distribution**: Concentration >50% in top holders = high risk
5. **Data Quality**: Helius blockchain data = high confidence, limited data = lower confidence
6. **Economic Viability**: Does the token have sustainable economics?

PROVIDE GENUINE REASONING - Don't just apply rules, think critically about:
- Is this token economically viable long-term?
- Are there signs of coordinated pump activity?
- Does the holder distribution suggest organic growth?
- Are the volume/liquidity ratios sustainable?
- What are the specific red flags or green flags?

Response format (JSON only):
{
  "riskScore": <number 1-10>,
  "confidence": <number 0.0-1.0>,
  "reasoning": "<detailed explanation of your analysis>",
  "findings": ["specific finding 1", "specific finding 2", ...]
}`;

    const response = await fetch(openRouter.baseURL + '/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouter.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://tokensentinel.ai',
        'X-Title': 'TokenSentinel AI Analysis',
      },
      body: JSON.stringify({
        model: openRouter.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1, // Low temperature for consistent analysis
        max_tokens: 2000, // Increased for DeepSeek R1T2 Chimera reasoning
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[ContractAuditor] OpenRouter API error: ${response.status} - ${errorText}`);
      throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    const message = data.choices[0]?.message;
    const content = message?.content;
    const reasoning = message?.reasoning;

    if (!content && !reasoning) {
      throw new Error('No response from DeepSeek R1T2 Chimera analysis');
    }

    // DeepSeek R1T2 Chimera may put the actual response in reasoning field
    const responseText = content || reasoning || '';
    console.log(`[ContractAuditor] DeepSeek R1T2 Chimera raw response: ${responseText.substring(0, 200)}...`);
    console.log(`[ContractAuditor] Response has content: ${!!content}, reasoning: ${!!reasoning}`);

    // Parse AI response with better error handling
    let aiResult: any;
    try {
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        // Try to extract JSON from markdown code blocks
        const codeBlockMatch = responseText.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
        if (codeBlockMatch) {
          aiResult = JSON.parse(codeBlockMatch[1]);
        } else {
          throw new Error('No JSON found in AI response');
        }
      } else {
        aiResult = JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error(`[ContractAuditor] Failed to parse AI response: ${parseError}`);
      console.error(`[ContractAuditor] Raw responseText: ${responseText}`);
      throw new Error(`Failed to parse AI response: ${parseError}`);
    }

    // Validate AI response structure
    const validatedResult = {
      riskScore: Math.max(1, Math.min(10, Number(aiResult.riskScore) || 5)),
      confidence: Math.max(0, Math.min(1, Number(aiResult.confidence) || 0.5)),
      reasoning: String(aiResult.reasoning || 'AI analysis completed'),
      findings: Array.isArray(aiResult.findings) ? aiResult.findings.map(String) : [],
    };

    console.log(`[ContractAuditor] DeepSeek R1-0528 validated result: Risk=${validatedResult.riskScore}, Confidence=${validatedResult.confidence}`);

    return validatedResult;

  } catch (error) {
    console.error('[ContractAuditor] DeepSeek AI analysis failed:', error);
    throw error;
  }
}

/**
 * Basic Solana token analysis (fallback)
 */
async function performBasicSolanaAnalysis(state: TokenAnalysisState): Promise<{
  riskScore: number;
  confidence: number;
  suspiciousFunctions: string[];
}> {
  let riskScore = 0;
  const suspiciousFunctions: string[] = [];

  // Age-based risk assessment
  if (state.tokenHunterResult?.ageHours) {
    if (state.tokenHunterResult.ageHours < 1) {
      riskScore += 3;
      suspiciousFunctions.push('Token created less than 1 hour ago');
    } else if (state.tokenHunterResult.ageHours < 24) {
      riskScore += 2;
      suspiciousFunctions.push('Token created less than 24 hours ago');
    } else if (state.tokenHunterResult.ageHours < 168) {
      riskScore += 1;
    }
  }

  // Market cap based risk
  if (state.tokenHunterResult?.marketCap) {
    if (state.tokenHunterResult.marketCap < 10000) {
      riskScore += 2;
      suspiciousFunctions.push('Very low market cap (<$10k)');
    } else if (state.tokenHunterResult.marketCap < 100000) {
      riskScore += 1;
    }
  }

  // Volume analysis
  if (state.tokenHunterResult?.volume24h && state.tokenHunterResult?.marketCap) {
    const volumeToMcapRatio = state.tokenHunterResult.volume24h / state.tokenHunterResult.marketCap;
    if (volumeToMcapRatio > 10) {
      riskScore += 1;
      suspiciousFunctions.push('Unusually high volume/market cap ratio');
    }
  }

  // Data quality assessment
  let confidence = 0.7;
  if (state.tokenHunterResult?.source?.includes('helius')) confidence += 0.2;
  if (state.tokenHunterResult?.marketCap && state.tokenHunterResult.marketCap > 0) confidence += 0.1;

  return {
    riskScore: Math.max(0, Math.min(10, riskScore)),
    confidence: Math.min(confidence, 1.0),
    suspiciousFunctions,
  };
}

/**
 * AI-powered contract analysis using DeepSeek R1
 */
async function performAIContractAnalysis(
  sourceCode: string,
  contractAddress: string
): Promise<{
  riskAssessment: string;
  additionalFindings: string[];
  confidence: number;
}> {
  try {
    const openRouter = createOpenRouterClient();
    
    const prompt = `You are a senior smart contract auditor specializing in rug pull detection. Analyze this Solana/Ethereum token contract for potential rug pull indicators.

Contract Address: ${contractAddress}

Source Code:
${sourceCode.substring(0, 8000)} // Truncate for API limits

Focus on:
1. Hidden mint functions or backdoors
2. Ownership patterns and centralization risks
3. Liquidity manipulation possibilities
4. Unusual transfer restrictions
5. Proxy patterns that could hide malicious code

Provide a risk assessment (1-10 scale) and list specific concerns.

Response format:
{
  "riskScore": <number 1-10>,
  "findings": ["finding1", "finding2"],
  "confidence": <number 0-1>
}`;

    const response = await fetch(openRouter.baseURL + '/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouter.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: openRouter.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1, // Low temperature for consistent analysis
        max_tokens: 1000,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }
    
    const data = await response.json();
    const content = data.choices[0]?.message?.content;
    
    if (!content) {
      throw new Error('No response from AI analysis');
    }
    
    // Parse AI response
    try {
      const parsed = JSON.parse(content);
      return {
        riskAssessment: `AI Risk Score: ${parsed.riskScore}/10`,
        additionalFindings: parsed.findings || [],
        confidence: parsed.confidence || 0.7,
      };
    } catch (parseError) {
      // Fallback if JSON parsing fails
      return {
        riskAssessment: content,
        additionalFindings: [],
        confidence: 0.5,
      };
    }
    
  } catch (error) {
    console.error('[ContractAuditor] AI analysis error:', error);
    return {
      riskAssessment: 'AI analysis unavailable',
      additionalFindings: [],
      confidence: 0.3,
    };
  }
}

/**
 * Combine static analysis with AI insights
 */
function combineAnalysisResults(
  staticAnalysis: any,
  aiAnalysis: any,
  contractData: any
): ContractAuditorResult {
  let riskScore = 0;
  
  // Base risk factors
  if (staticAnalysis.hiddenMintDetected) riskScore += 3;
  if (!staticAnalysis.ownershipRenounced) riskScore += 2;
  if (staticAnalysis.blacklistFunction) riskScore += 2;
  if (staticAnalysis.pausable) riskScore += 1;
  if (staticAnalysis.maxTransactionLimit) riskScore += 1;
  if (contractData.isProxy) riskScore += 1;
  if (!contractData.sourceCode) riskScore += 2; // Unverified contract
  
  // AI risk adjustment
  if (aiAnalysis.riskAssessment.includes('high risk')) riskScore += 1;
  if (aiAnalysis.riskAssessment.includes('low risk')) riskScore -= 1;
  
  // Ensure risk score is within bounds
  riskScore = Math.max(0, Math.min(10, riskScore));
  
  // Calculate confidence
  const confidence = contractData.sourceCode ? 
    Math.min(0.9, 0.6 + (aiAnalysis.confidence * 0.3)) : 0.3;
  
  return {
    riskScore,
    hiddenMintDetected: staticAnalysis.hiddenMintDetected,
    ownershipRenounced: staticAnalysis.ownershipRenounced,
    liquidityLocked: false, // This would require additional on-chain analysis
    suspiciousFunctions: [
      ...staticAnalysis.suspiciousFunctions,
      ...aiAnalysis.additionalFindings,
    ],
    confidence,
    contractVerified: !!contractData.sourceCode,
    proxyContract: contractData.isProxy,
    pausable: staticAnalysis.pausable,
    blacklistFunction: staticAnalysis.blacklistFunction,
    maxTransactionLimit: staticAnalysis.maxTransactionLimit,
  };
}
