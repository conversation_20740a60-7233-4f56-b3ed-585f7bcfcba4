/**
 * @fileOverview TokenSentinel LangGraph Workflow - Multi-agent rug detection system
 * Orchestrates Token Hunter, Contract Auditor, On-Chain Analyst, and Social Sentiment agents
 */

import { z } from 'zod';
import { 
  TokenAnalysisState, 
  determineNextAgent, 
  calculateOverallRisk, 
  predictPriceDirection 
} from '../agents/supervisor';
import { executeTokenHunter } from '../agents/token-hunter';
import { executeContractAuditor } from '../agents/contract-auditor';
import { executeOnChainAnalyst } from '../agents/onchain-analyst';
import { executeSocialSentiment } from '../agents/social-sentiment';

// Input schema for TokenSentinel workflow
export const TokenSentinelInput = z.object({
  tokenAddress: z.string().min(1, 'Token address is required'),
  tokenName: z.string().optional(),
  tokenSymbol: z.string().optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH']).default('MEDIUM'),
  skipSocialAnalysis: z.boolean().default(false),
});

export type TokenSentinelInput = z.infer<typeof TokenSentinelInput>;

// Output schema for TokenSentinel workflow
export const TokenSentinelOutput = z.object({
  tokenAddress: z.string(),
  tokenName: z.string(),
  tokenSymbol: z.string(),
  
  // Risk assessment
  overallRiskScore: z.number().min(0).max(10),
  rugPullProbability: z.number().min(0).max(1),
  alertLevel: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  confidence: z.number().min(0).max(1),
  
  // Price prediction
  priceDirection: z.enum(['UP', 'DOWN', 'SIDEWAYS']),
  
  // Agent results
  tokenHunterResult: z.any().optional(),
  contractAuditorResult: z.any().optional(),
  onChainAnalystResult: z.any().optional(),
  socialSentimentResult: z.any().optional(),
  
  // Execution metadata
  executionTime: z.number(),
  agentsExecuted: z.array(z.string()),
  errors: z.array(z.string()),
  
  // Alert data
  shouldAlert: z.boolean(),
  alertMessage: z.string().optional(),
  recommendedAction: z.string().optional(),
});

export type TokenSentinelOutput = z.infer<typeof TokenSentinelOutput>;

/**
 * Main TokenSentinel workflow execution
 */
export async function executeTokenSentinel(
  input: TokenSentinelInput
): Promise<TokenSentinelOutput> {
  const startTime = Date.now();
  const agentsExecuted: string[] = [];
  
  console.log(`[TokenSentinel] Starting analysis for ${input.tokenAddress}`);
  
  // Initialize state
  let state: TokenAnalysisState = {
    tokenAddress: input.tokenAddress,
    tokenName: input.tokenName,
    tokenSymbol: input.tokenSymbol,
    completed: false,
    errors: [],
  };
  
  try {
    // Execute agents in sequence based on supervisor logic
    while (!state.completed && state.errors.length < 3) {
      const nextAgent = determineNextAgent(state);
      
      if (!nextAgent) {
        // All agents completed, finalize analysis
        state.completed = true;
        break;
      }
      
      // Prevent duplicate agent execution
      if (agentsExecuted.includes(nextAgent)) {
        console.log(`[TokenSentinel] Agent ${nextAgent} already executed, skipping to prevent loop`);
        state.errors.push(`Agent ${nextAgent} attempted duplicate execution`);
        break;
      }

      console.log(`[TokenSentinel] Executing agent: ${nextAgent} (${agentsExecuted.length + 1}/10)`);
      agentsExecuted.push(nextAgent);
      
      // Execute the appropriate agent
      switch (nextAgent) {
        case 'token_hunter':
          state = { ...state, ...(await executeTokenHunter(state)) };
          break;
          
        case 'contract_auditor':
          state = { ...state, ...(await executeContractAuditor(state)) };
          break;
          
        case 'onchain_analyst':
          state = { ...state, ...(await executeOnChainAnalyst(state)) };
          break;
          
        case 'social_sentiment':
          if (!input.skipSocialAnalysis) {
            state = { ...state, ...(await executeSocialSentiment(state)) };
          } else {
            // Skip social analysis and move to risk aggregation
            state.currentAgent = 'social_sentiment';
            state.nextAgent = 'risk_aggregator';
          }
          break;
          
        case 'risk_aggregator':
          // Calculate final risk assessment
          const riskAssessment = calculateOverallRisk(state);
          const priceDirection = predictPriceDirection(state);
          
          state.overallRiskScore = riskAssessment.riskScore;
          state.rugPullProbability = riskAssessment.rugPullProbability;
          state.alertLevel = riskAssessment.alertLevel;
          state.confidence = riskAssessment.confidence;
          state.priceDirection = priceDirection;
          state.completed = true;
          
          agentsExecuted.push('risk_aggregator');
          break;
          
        default:
          state.errors.push(`Unknown agent: ${nextAgent}`);
          break;
      }
      
      // Safety check to prevent infinite loops
      if (agentsExecuted.length > 10) {
        console.log(`[TokenSentinel] Maximum agent execution limit reached. Agents executed: ${agentsExecuted.join(', ')}`);
        console.log(`[TokenSentinel] Current state: tokenHunter=${!!state.tokenHunterResult}, contract=${!!state.contractAuditorResult}, onchain=${!!state.onChainAnalystResult}, social=${!!state.socialSentimentResult}, risk=${!!state.overallRiskScore}`);
        state.errors.push('Maximum agent execution limit reached');

        // Force completion with risk aggregation if we have enough data
        if (state.tokenHunterResult && state.contractAuditorResult && state.onChainAnalystResult && !state.overallRiskScore) {
          console.log(`[TokenSentinel] Forcing risk aggregation with available data`);
          const riskAssessment = calculateOverallRisk(state);
          const priceDirection = predictPriceDirection(state);

          state.overallRiskScore = riskAssessment.riskScore;
          state.rugPullProbability = riskAssessment.rugPullProbability;
          state.alertLevel = riskAssessment.alertLevel;
          state.confidence = riskAssessment.confidence;
          state.priceDirection = priceDirection;
          state.completed = true;

          agentsExecuted.push('risk_aggregator_forced');
        }
        break;
      }
    }
    
    // Generate alert decision and message
    const alertDecision = generateAlertDecision(state);
    
    const executionTime = Date.now() - startTime;
    
    console.log(`[TokenSentinel] Analysis completed in ${executionTime}ms`);
    console.log(`[TokenSentinel] Risk Score: ${state.overallRiskScore}/10`);
    console.log(`[TokenSentinel] Alert Level: ${state.alertLevel}`);
    
    return {
      tokenAddress: state.tokenAddress,
      tokenName: state.tokenName || 'Unknown',
      tokenSymbol: state.tokenSymbol || 'Unknown',
      overallRiskScore: state.overallRiskScore || 5,
      rugPullProbability: state.rugPullProbability || 0.5,
      alertLevel: state.alertLevel || 'MEDIUM',
      confidence: state.confidence || 0.5,
      priceDirection: state.priceDirection || 'SIDEWAYS',
      tokenHunterResult: state.tokenHunterResult,
      contractAuditorResult: state.contractAuditorResult,
      onChainAnalystResult: state.onChainAnalystResult,
      socialSentimentResult: state.socialSentimentResult,
      executionTime,
      agentsExecuted,
      errors: state.errors,
      shouldAlert: alertDecision.shouldAlert,
      alertMessage: alertDecision.message,
      recommendedAction: alertDecision.action,
    };
    
  } catch (error) {
    console.error('[TokenSentinel] Workflow error:', error);
    
    const executionTime = Date.now() - startTime;
    
    return {
      tokenAddress: state.tokenAddress,
      tokenName: state.tokenName || 'Unknown',
      tokenSymbol: state.tokenSymbol || 'Unknown',
      overallRiskScore: 10, // Maximum risk for failed analysis
      rugPullProbability: 1,
      alertLevel: 'CRITICAL',
      confidence: 0,
      priceDirection: 'DOWN',
      executionTime,
      agentsExecuted,
      errors: [...state.errors, `Workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      shouldAlert: true,
      alertMessage: 'Analysis failed - treat as high risk',
      recommendedAction: 'AVOID',
    };
  }
}

/**
 * Generate alert decision based on analysis results
 */
function generateAlertDecision(state: TokenAnalysisState): {
  shouldAlert: boolean;
  message: string;
  action: string;
} {
  const riskScore = state.overallRiskScore || 5;
  const alertLevel = state.alertLevel || 'MEDIUM';
  const confidence = state.confidence || 0.5;
  
  // Determine if we should send an alert
  let shouldAlert = false;
  let message = '';
  let action = '';
  
  if (alertLevel === 'CRITICAL') {
    shouldAlert = true;
    message = `🚨 CRITICAL RUG PULL RISK DETECTED\n` +
              `Token: ${state.tokenName} (${state.tokenSymbol})\n` +
              `Risk Score: ${riskScore.toFixed(1)}/10\n` +
              `Confidence: ${(confidence * 100).toFixed(0)}%`;
    action = 'AVOID - DO NOT INVEST';
  } else if (alertLevel === 'HIGH') {
    shouldAlert = true;
    message = `⚠️ HIGH RISK TOKEN DETECTED\n` +
              `Token: ${state.tokenName} (${state.tokenSymbol})\n` +
              `Risk Score: ${riskScore.toFixed(1)}/10\n` +
              `Confidence: ${(confidence * 100).toFixed(0)}%`;
    action = 'CAUTION - High risk of rug pull';
  } else if (alertLevel === 'MEDIUM' && riskScore > 6) {
    shouldAlert = true;
    message = `⚡ MEDIUM RISK TOKEN\n` +
              `Token: ${state.tokenName} (${state.tokenSymbol})\n` +
              `Risk Score: ${riskScore.toFixed(1)}/10\n` +
              `Price Direction: ${state.priceDirection}`;
    action = 'RESEARCH - Proceed with caution';
  } else if (alertLevel === 'LOW' && state.priceDirection === 'UP') {
    shouldAlert = true;
    message = `✅ LOW RISK TOKEN - POTENTIAL OPPORTUNITY\n` +
              `Token: ${state.tokenName} (${state.tokenSymbol})\n` +
              `Risk Score: ${riskScore.toFixed(1)}/10\n` +
              `Price Direction: UP`;
    action = 'OPPORTUNITY - Consider for investment';
  }
  
  // Add specific risk factors to message
  if (shouldAlert && state.contractAuditorResult?.hiddenMintDetected) {
    message += '\n🔴 Hidden mint function detected';
  }
  
  if (shouldAlert && state.onChainAnalystResult?.holderConcentration > 0.8) {
    message += '\n🔴 High holder concentration';
  }
  
  if (shouldAlert && state.socialSentimentResult?.botActivityDetected) {
    message += '\n🔴 Bot activity detected';
  }
  
  return { shouldAlert, message, action };
}

/**
 * Batch process multiple tokens
 */
export async function batchAnalyzeTokens(
  tokens: TokenSentinelInput[]
): Promise<TokenSentinelOutput[]> {
  console.log(`[TokenSentinel] Batch analyzing ${tokens.length} tokens`);
  
  // Process tokens in parallel with concurrency limit
  const BATCH_SIZE = 5;
  const results: TokenSentinelOutput[] = [];
  
  for (let i = 0; i < tokens.length; i += BATCH_SIZE) {
    const batch = tokens.slice(i, i + BATCH_SIZE);
    
    const batchResults = await Promise.allSettled(
      batch.map(token => executeTokenSentinel(token))
    );
    
    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        console.error('[TokenSentinel] Batch analysis error:', result.reason);
        // Add error result
        results.push({
          tokenAddress: 'unknown',
          tokenName: 'Error',
          tokenSymbol: 'ERROR',
          overallRiskScore: 10,
          rugPullProbability: 1,
          alertLevel: 'CRITICAL',
          confidence: 0,
          priceDirection: 'DOWN',
          executionTime: 0,
          agentsExecuted: [],
          errors: [`Batch processing failed: ${result.reason}`],
          shouldAlert: true,
          alertMessage: 'Batch analysis failed',
          recommendedAction: 'AVOID',
        });
      }
    }
    
    // Rate limiting between batches
    if (i + BATCH_SIZE < tokens.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
}

/**
 * Real-time token monitoring workflow
 */
export async function startRealTimeMonitoring(
  alertCallback: (result: TokenSentinelOutput) => void
): Promise<void> {
  console.log('[TokenSentinel] Starting real-time monitoring...');
  
  // This would integrate with your WebSocket service to monitor new tokens
  // and automatically analyze them as they appear
  
  // Implementation would:
  // 1. Connect to PumpFun WebSocket
  // 2. Filter new tokens by criteria
  // 3. Automatically run TokenSentinel analysis
  // 4. Send alerts for high-risk tokens
}
