/**
 * @fileOverview Collaborative TokenSentinel Workflow - True Multi-Agent System
 * Implements agent-to-agent communication, consensus building, and iterative refinement
 */

import { z } from 'zod';
import { 
  TokenAnalysisState, 
  calculateOverallRisk, 
  predictPriceDirection 
} from '../agents/supervisor';
import { executeTokenHunter } from '../agents/token-hunter';
import { executeContractAuditor } from '../agents/contract-auditor';
import { executeOnChainAnalyst } from '../agents/onchain-analyst';
import { executeSocialSentiment } from '../agents/social-sentiment';
import { 
  agentCommunicationBus, 
  ConsensusBuilder, 
  AgentMessage, 
  AgentType 
} from '../agents/communication';

// Enhanced input schema
export const CollaborativeTokenSentinelInput = z.object({
  tokenAddress: z.string().min(1, 'Token address is required'),
  tokenName: z.string().optional(),
  tokenSymbol: z.string().optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH']).default('MEDIUM'),
  skipSocialAnalysis: z.boolean().default(false),
  maxIterations: z.number().default(3), // Maximum consensus iterations
  consensusThreshold: z.number().default(0.7), // Minimum agreement for consensus
});

export type CollaborativeTokenSentinelInput = z.infer<typeof CollaborativeTokenSentinelInput>;

// Enhanced output schema with consensus data
export const CollaborativeTokenSentinelOutput = z.object({
  tokenAddress: z.string(),
  tokenName: z.string(),
  tokenSymbol: z.string(),
  
  // Risk assessment with consensus
  overallRiskScore: z.number().min(0).max(10),
  rugPullProbability: z.number().min(0).max(1),
  alertLevel: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  confidence: z.number().min(0).max(1),
  consensusAgreement: z.number().min(0).max(1), // How much agents agreed
  
  // Price prediction
  priceDirection: z.enum(['UP', 'DOWN', 'SIDEWAYS']),
  
  // Agent results
  tokenHunterResult: z.any().optional(),
  contractAuditorResult: z.any().optional(),
  onChainAnalystResult: z.any().optional(),
  socialSentimentResult: z.any().optional(),
  
  // Collaboration metadata
  agentMessages: z.array(z.any()),
  consensusResults: z.any(),
  iterations: z.number(),
  
  // Execution metadata
  executionTime: z.number(),
  agentsExecuted: z.array(z.string()),
  errors: z.array(z.string()),
  shouldAlert: z.boolean(),
  alertMessage: z.string(),
  recommendedAction: z.string(),
});

export type CollaborativeTokenSentinelOutput = z.infer<typeof CollaborativeTokenSentinelOutput>;

/**
 * Collaborative TokenSentinel workflow with multi-agent communication
 */
export async function executeCollaborativeTokenSentinel(
  input: CollaborativeTokenSentinelInput
): Promise<CollaborativeTokenSentinelOutput> {
  const startTime = Date.now();
  
  // Clear previous communication
  agentCommunicationBus.clear();
  const consensusBuilder = new ConsensusBuilder();
  
  // Initialize state
  let state: TokenAnalysisState = {
    tokenAddress: input.tokenAddress,
    tokenName: input.tokenName || '',
    tokenSymbol: input.tokenSymbol || '',
    errors: [],
    completed: false,
  };

  const agentsExecuted: string[] = [];
  let iterations = 0;
  
  console.log(`[CollaborativeTokenSentinel] Starting collaborative analysis for ${input.tokenAddress}`);

  try {
    // Phase 1: Initial Data Gathering
    console.log(`[CollaborativeTokenSentinel] Phase 1: Initial Data Gathering`);
    
    // Execute core agents in parallel for initial data
    const [tokenHunterResult, contractAuditorResult, onChainResult] = await Promise.allSettled([
      executeTokenHunter(state),
      executeContractAuditor(state),
      executeOnChainAnalyst(state),
    ]);

    // Update state with results
    if (tokenHunterResult.status === 'fulfilled') {
      state = { ...state, ...tokenHunterResult.value };
      agentsExecuted.push('token_hunter');
      
      // Add findings to consensus
      if (state.tokenHunterResult) {
        consensusBuilder.addFinding('marketCap', 'token_hunter', state.tokenHunterResult.marketCap, 0.9);
        consensusBuilder.addFinding('ageHours', 'token_hunter', state.tokenHunterResult.ageHours, 0.9);
        consensusBuilder.addFinding('volume24h', 'token_hunter', state.tokenHunterResult.volume24h, 0.8);
      }
    }

    if (contractAuditorResult.status === 'fulfilled') {
      state = { ...state, ...contractAuditorResult.value };
      agentsExecuted.push('contract_auditor');
      
      // Add findings to consensus
      if (state.contractAuditorResult) {
        consensusBuilder.addFinding('contractRisk', 'contract_auditor', state.contractAuditorResult.riskScore, state.contractAuditorResult.confidence);
      }
    }

    if (onChainResult.status === 'fulfilled') {
      state = { ...state, ...onChainResult.value };
      agentsExecuted.push('onchain_analyst');
      
      // Add findings to consensus
      if (state.onChainAnalystResult) {
        consensusBuilder.addFinding('holderConcentration', 'onchain_analyst', state.onChainAnalystResult.holderConcentration, 0.8);
        consensusBuilder.addFinding('liquidityUSD', 'onchain_analyst', state.onChainAnalystResult.liquidityUSD, 0.9);
      }
    }

    // Phase 2: Social Analysis (if not skipped)
    if (!input.skipSocialAnalysis) {
      console.log(`[CollaborativeTokenSentinel] Phase 2: Social Analysis`);
      const socialResult = await executeSocialSentiment(state);
      state = { ...state, ...socialResult };
      agentsExecuted.push('social_sentiment');
      
      if (state.socialSentimentResult) {
        consensusBuilder.addFinding('socialSentiment', 'social_sentiment', state.socialSentimentResult.sentimentScore, 0.6);
      }
    }

    // Phase 3: Collaborative Refinement
    console.log(`[CollaborativeTokenSentinel] Phase 3: Collaborative Refinement`);
    
    for (iterations = 1; iterations <= input.maxIterations; iterations++) {
      console.log(`[CollaborativeTokenSentinel] Iteration ${iterations}/${input.maxIterations}`);
      
      // Build current consensus
      const currentConsensus = consensusBuilder.getAllConsensus();
      let needsRefinement = false;
      
      // Check for disagreements that need resolution
      currentConsensus.forEach((consensus, key) => {
        if (consensus.agreement < input.consensusThreshold) {
          console.log(`[CollaborativeTokenSentinel] Low agreement on ${key}: ${consensus.agreement}`);
          needsRefinement = true;
          
          // Trigger agent communication for refinement
          if (key === 'contractRisk' && consensus.agreement < 0.6) {
            // Request validation from onchain analyst
            agentCommunicationBus.sendMessage(
              'contract_auditor',
              'onchain_analyst',
              'VALIDATION',
              { finding: consensus.value, reason: 'Low consensus on contract risk' },
              0.7
            );
          }
        }
      });
      
      if (!needsRefinement) {
        console.log(`[CollaborativeTokenSentinel] Consensus reached after ${iterations} iterations`);
        break;
      }
      
      // Allow time for agent communication
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Phase 4: Final Risk Aggregation
    console.log(`[CollaborativeTokenSentinel] Phase 4: Final Risk Aggregation`);
    
    const riskAssessment = calculateOverallRisk(state);
    const priceDirection = predictPriceDirection(state);
    
    // Calculate consensus agreement
    const allConsensus = consensusBuilder.getAllConsensus();
    const totalAgreement = Array.from(allConsensus.values()).reduce((sum, c) => sum + c.agreement, 0);
    const avgAgreement = allConsensus.size > 0 ? totalAgreement / allConsensus.size : 0;

    // Determine if alert should be sent
    const shouldAlert = riskAssessment.alertLevel === 'HIGH' || riskAssessment.alertLevel === 'CRITICAL';
    const alertMessage = shouldAlert 
      ? `${riskAssessment.alertLevel} risk detected for ${state.tokenName} (${state.tokenSymbol})`
      : '';

    const executionTime = Date.now() - startTime;
    
    console.log(`[CollaborativeTokenSentinel] Analysis completed in ${executionTime}ms`);
    console.log(`[CollaborativeTokenSentinel] Risk Score: ${riskAssessment.riskScore}/10`);
    console.log(`[CollaborativeTokenSentinel] Consensus Agreement: ${(avgAgreement * 100).toFixed(1)}%`);
    console.log(`[CollaborativeTokenSentinel] Alert Level: ${riskAssessment.alertLevel}`);

    return {
      tokenAddress: input.tokenAddress,
      tokenName: state.tokenName || 'Unknown Token',
      tokenSymbol: state.tokenSymbol || 'UNKNOWN',
      
      overallRiskScore: riskAssessment.riskScore,
      rugPullProbability: riskAssessment.rugPullProbability,
      alertLevel: riskAssessment.alertLevel,
      confidence: riskAssessment.confidence,
      consensusAgreement: avgAgreement,
      
      priceDirection,
      
      tokenHunterResult: state.tokenHunterResult,
      contractAuditorResult: state.contractAuditorResult,
      onChainAnalystResult: state.onChainAnalystResult,
      socialSentimentResult: state.socialSentimentResult,
      
      agentMessages: agentCommunicationBus.getAllMessages(),
      consensusResults: Object.fromEntries(allConsensus),
      iterations,
      
      executionTime,
      agentsExecuted,
      errors: state.errors,
      shouldAlert,
      alertMessage,
      recommendedAction: shouldAlert ? 'AVOID' : 'MONITOR',
    };

  } catch (error) {
    console.error('[CollaborativeTokenSentinel] Analysis failed:', error);
    
    return {
      tokenAddress: input.tokenAddress,
      tokenName: state.tokenName || 'Unknown Token',
      tokenSymbol: state.tokenSymbol || 'UNKNOWN',
      
      overallRiskScore: 10, // Maximum risk on error
      rugPullProbability: 1.0,
      alertLevel: 'CRITICAL',
      confidence: 0,
      consensusAgreement: 0,
      
      priceDirection: 'DOWN',
      
      tokenHunterResult: state.tokenHunterResult,
      contractAuditorResult: state.contractAuditorResult,
      onChainAnalystResult: state.onChainAnalystResult,
      socialSentimentResult: state.socialSentimentResult,
      
      agentMessages: agentCommunicationBus.getAllMessages(),
      consensusResults: {},
      iterations,
      
      executionTime: Date.now() - startTime,
      agentsExecuted,
      errors: [...state.errors, `Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      shouldAlert: true,
      alertMessage: 'Analysis failed - treat as high risk',
      recommendedAction: 'AVOID',
    };
  }
}
