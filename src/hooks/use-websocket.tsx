'use client';

import { useState, useEffect, useContext, createContext, ReactNode, useCallback } from 'react';
import { WebSocketService, webSocketService } from '@/lib/websocket-service';

type WebSocketContextType = {
  [key: string]: {
    lastMessage: any;
    isConnected: boolean;
    subscribe: (message: any) => void;
  }
};

const WebSocketContext = createContext<WebSocketContextType | null>(null);

const sources: { [key: string]: WebSocketService } = {
  kraken: webSocketService.getInstance('wss://ws.kraken.com/'),
  pumpfun: webSocketService.getInstance('wss://pumpportal.fun/api/data'),
};

export function WebSocketProvider({ children }: { children: ReactNode }) {
  const [contexts, setContexts] = useState<WebSocketContextType>({});

  useEffect(() => {
    const newContexts: WebSocketContextType = {};
    for (const key in sources) {
      const source = sources[key];
      newContexts[key] = {
        lastMessage: null,
        isConnected: source.isConnected(),
        subscribe: source.subscribe.bind(source),
      };
    }
    setContexts(newContexts);
    
    const listeners: { [key: string]: (data: any) => void } = {};
    const connectionListeners: { [key: string]: (status: boolean) => void } = {};

    for (const key in sources) {
        const source = sources[key];
        
        listeners[key] = (data: any) => {
            setContexts(prev => ({
                ...prev,
                [key]: { ...prev[key], lastMessage: data }
            }));
        };

        connectionListeners[key] = (status: boolean) => {
             setContexts(prev => ({
                ...prev,
                [key]: { ...prev[key], isConnected: status }
            }));
        }

        source.addListener(listeners[key]);
        source.addConnectionListener(connectionListeners[key]);
    }

    return () => {
      for (const key in sources) {
        const source = sources[key];
        source.removeListener(listeners[key]);
        source.removeConnectionListener(connectionListeners[key]);
        // Don't close the connection here, as it's a shared singleton
      }
    };
  }, []);

  return (
    <WebSocketContext.Provider value={contexts}>
      {children}
    </WebSocketContext.Provider>
  );
}

export function useWebSocket(sourceKey: 'kraken' | 'pumpfun') {
  const context = useContext(WebSocketContext);
  
  const sourceData = context?.[sourceKey];

  const { lastMessage, isConnected, subscribe } = sourceData || { 
    lastMessage: null, 
    isConnected: false, 
    subscribe: () => {} 
  };
  
  const memoizedSubscribe = useCallback(subscribe, [subscribe]);

  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }

  return { lastMessage, isConnected, subscribe: memoizedSubscribe };
}
