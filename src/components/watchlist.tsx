"use client";

import { useEffect, useState } from "react";
import { Bitcoin, <PERSON>s, Coins, Dog } from "lucide-react";
import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuItem, SidebarMenuButton } from "./ui/sidebar";
import { Skeleton } from "./ui/skeleton";
import { useWebSocket } from "@/hooks/use-websocket.tsx";
import type { KrakenTicker } from "@/lib/types";
import { TOKEN_MAP } from "@/lib/kraken-pairs";

type WatchlistItem = {
  name: string;
  ticker: string;
  price: string;
  change: string;
  status: "up" | "down" | "neutral";
};

const initialWatchlistItems: WatchlistItem[] = [
  { name: "Bitcoin", ticker: "XBT/USD", price: "$0.00", change: "+0.0%", status: "neutral" },
  { name: "Ethereum", ticker: "ETH/USD", price: "$0.00", change: "+0.0%", status: "neutral" },
  { name: "<PERSON><PERSON>", ticker: "SOL/USD", price: "$0.00", change: "+0.0%", status: "neutral" },
  { name: "<PERSON>ecoi<PERSON>", ticker: "DOGE/USD", price: "$0.00", change: "+0.0%", status: "neutral" },
];

const Icon = ({ ticker }: { ticker: string }) => {
    const symbol = ticker.split('/')[0];
    if (symbol === 'XBT') return <Bitcoin className="text-orange-400" />;
    if (symbol === 'ETH') return <Waves className="text-blue-400" />;
    if (symbol === 'SOL') return <Coins className="text-purple-400" />;
    if (symbol === 'DOGE') return <Dog className="text-yellow-400" />;
    // A simple, consistent fallback icon
    return <div className="w-4 h-4 rounded-full bg-muted-foreground" />;
}

export function Watchlist() {
  const [watchlist, setWatchlist] = useState<WatchlistItem[]>(initialWatchlistItems);
  const [isLoading, setIsLoading] = useState(true);
  const { lastMessage, isConnected, subscribe } = useWebSocket('kraken');

  useEffect(() => {
    const pairs = initialWatchlistItems.map(item => item.ticker);
    subscribe({
        event: 'subscribe',
        pair: pairs,
        subscription: { name: 'ticker' },
    });
  }, [subscribe]);

  useEffect(() => {
    if (isConnected) {
        // Give a bit of time for data to flow in
        const timer = setTimeout(() => setIsLoading(false), 1500);
        return () => clearTimeout(timer);
    }
  }, [isConnected]);

  useEffect(() => {
    if (lastMessage && Array.isArray(lastMessage) && lastMessage[2] === 'ticker') {
        const pair = lastMessage[3];
        const ticker = lastMessage[1] as KrakenTicker;
        
        const price = parseFloat(ticker.c[0]);
        const openPrice = parseFloat(ticker.o[1]); // Use 24h open price
        const change = ((price - openPrice) / openPrice) * 100;
        
        setWatchlist(prevList =>
          prevList.map(item =>
            item.ticker === pair
              ? {
                  ...item,
                  price: `$${price.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`,
                  change: `${change.toFixed(2)}%`,
                  status: change > 0 ? 'up' : 'down',
                }
              : item
          )
        );
      }
  }, [lastMessage]);

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Watchlist</SidebarGroupLabel>
      <SidebarMenu>
        {isLoading ? (
          Array.from({ length: 4 }).map((_, i) => (
            <SidebarMenuItem key={i}>
              <SidebarMenuButton size="sm" className="h-auto py-1.5 justify-start">
                  <div className="flex items-center gap-2 flex-1">
                     <Skeleton className="w-4 h-4 rounded-full" />
                     <div className="flex flex-col gap-1">
                         <Skeleton className="w-16 h-4" />
                         <Skeleton className="w-8 h-3" />
                     </div>
                  </div>
                   <div className="flex flex-col items-end gap-1">
                     <Skeleton className="w-12 h-4" />
                     <Skeleton className="w-10 h-3" />
                  </div>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))
        ) : (
          watchlist.map((item) => (
            <SidebarMenuItem key={item.ticker}>
              <SidebarMenuButton size="sm" className="h-auto py-1.5 justify-start">
                <div className="flex items-center gap-2 flex-1">
                  <Icon ticker={item.ticker} />
                  <div className="flex flex-col items-start">
                      <span className="font-medium">{item.name}</span>
                      <span className="text-xs text-muted-foreground">{TOKEN_MAP[item.ticker]?.symbol || item.ticker}</span>
                  </div>
                </div>
                <div className="flex flex-col items-end">
                  <span className="font-mono text-xs">{item.price}</span>
                  <span className={`text-xs ${item.status === 'up' ? 'text-green-400' : 'text-red-400'}`}>{item.change}</span>
                </div>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))
        )}
      </SidebarMenu>
    </SidebarGroup>
  );
}
