
"use client";

import { useEffect, useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { useWebSocket } from "@/hooks/use-websocket.tsx";
import type { KrakenTicker } from "@/lib/types";
import { ScrollArea } from "./ui/scroll-area";
import { TOKEN_MAP } from "@/lib/kraken-pairs";

type TickerData = {
  price: string;
  change24h: string;
  volume24h: string;
  changeType: "positive" | "negative" | "neutral";
};

type MarketData = {
  [pair: string]: TickerData;
};

export function MarketFeed() {
  const [marketData, setMarketData] = useState<MarketData>({});
  const [isLoading, setIsLoading] = useState(true);
  const [activePairs, setActivePairs] = useState<string[]>([]);
  const { lastMessage, subscribe } = useWebSocket('kraken');

  useEffect(() => {
    const fetchAssetPairs = async () => {
      try {
        // Fetch the list of all tradable asset pairs from Kraken's public REST API
        const response = await fetch('https://api.kraken.com/0/public/AssetPairs');
        const data = await response.json();
        if (data.error && data.error.length > 0) {
          console.error("Error fetching asset pairs:", data.error);
          return;
        }

        const result = data.result;
        const wsPairs = Object.values(result)
          // Filter for pairs that are quoted in USD and have a wsname
          .filter((pair: any) => pair.quote === 'ZUSD' && pair.wsname)
          .map((pair: any) => pair.wsname);

        // Initialize market data for the fetched pairs
        const initialData: MarketData = {};
        wsPairs.forEach(pair => {
            initialData[pair] = { price: "$0.00", change24h: "0.00%", volume24h: "0", changeType: "neutral" };
        });

        setMarketData(initialData);
        setActivePairs(wsPairs);
        
        // Subscribe to the valid WebSocket pairs
        if (wsPairs.length > 0) {
          subscribe({
              event: 'subscribe',
              pair: wsPairs,
              subscription: { name: 'ticker' },
          });
        }
      } catch (error) {
        console.error("Failed to fetch asset pairs:", error);
      } finally {
        // Allow a moment for subscriptions to establish before hiding skeleton
        setTimeout(() => setIsLoading(false), 2000);
      }
    };

    fetchAssetPairs();
  }, [subscribe]);

  useEffect(() => {
    if (lastMessage && Array.isArray(lastMessage) && lastMessage[2] === 'ticker') {
        const pair = lastMessage[3];
        const ticker = lastMessage[1] as KrakenTicker;
        
        if (activePairs.includes(pair)) {
            const price = parseFloat(ticker.c[0]);
            const openPrice = parseFloat(ticker.o[1]);
            const change = openPrice !== 0 ? ((price - openPrice) / openPrice) * 100 : 0;

            setMarketData(prevData => ({
              ...prevData,
              [pair]: {
                price: price.toLocaleString('en-US', { style: 'currency', currency: 'USD' }),
                change24h: `${change.toFixed(2)}%`,
                volume24h: parseFloat(ticker.v[1]).toLocaleString('en-US', { notation: 'compact', compactDisplay: 'short' }),
                changeType: change > 0 ? 'positive' : change < 0 ? 'negative' : 'neutral',
              },
            }));
        }
      }
  }, [lastMessage, activePairs]);

  const sortedMarketData = useMemo(() => {
    return Object.entries(marketData)
      .filter(([pair, data]) => data.price !== '$0.00') // Filter out tokens with a price of 0
      .sort(([pairA], [pairB]) => {
        const volA = parseFloat(marketData[pairA]?.volume24h?.replace(/[^0-9.]/g, '')) || 0;
        const volB = parseFloat(marketData[pairB]?.volume24h?.replace(/[^0-9.]/g, '')) || 0;
        return volB - volA;
      });
  }, [marketData]);
  
  const displayToken = (pair: string) => {
    const tokenInfo = TOKEN_MAP[pair];
    if (tokenInfo) return tokenInfo;

    const base = pair.split('/')[0];
    const symbol = base.startsWith('X') ? base.substring(1) : base;
    return { name: symbol, symbol };
  }


  return (
    <ScrollArea className="h-[600px] rounded-lg border">
        <Table>
        <TableHeader className="sticky top-0 bg-card z-10">
            <TableRow>
            <TableHead>Token</TableHead>
            <TableHead className="text-right">Price (USD)</TableHead>
            <TableHead className="text-right">24h Change</TableHead>
            <TableHead className="text-right hidden sm:table-cell">24h Volume</TableHead>
            </TableRow>
        </TableHeader>
        <TableBody>
            {isLoading ? (
              Array.from({ length: 20 }).map((_, i) => (
                <TableRow key={i}>
                  <TableCell><Skeleton className="h-5 w-28" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24 float-right" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-16 float-right" /></TableCell>
                  <TableCell className="hidden sm:table-cell"><Skeleton className="h-5 w-20 float-right" /></TableCell>
                </TableRow>
              ))
            ) : (
                sortedMarketData.map(([pair, item]) => {
                  const token = displayToken(pair);
                  return (
                    <TableRow key={pair}>
                        <TableCell>
                          <div className="font-medium">{token.name}</div>
                          <div className="text-xs text-muted-foreground">{token.symbol}</div>
                        </TableCell>
                        <TableCell className="text-right font-mono">{item.price}</TableCell>
                        <TableCell className={`text-right font-mono ${item.changeType === 'positive' ? 'text-green-400' : item.changeType === 'negative' ? 'text-red-400' : ''}`}>
                            {item.change24h}
                        </TableCell>
                        <TableCell className="text-right hidden sm:table-cell font-mono">{item.volume24h}</TableCell>
                    </TableRow>
                  )
                })
            )}
        </TableBody>
        </Table>
    </ScrollArea>
  );
}
