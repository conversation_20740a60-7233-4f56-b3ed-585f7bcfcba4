'use client';

import {useActionState} from 'react';
import {useFormStatus} from 'react-dom';
import {getTokenReportAction, type FormState} from '@/lib/actions';
import {Button} from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {
  Loader2,
  AlertTriangle,
  BotMessageSquare,
  TrendingUp,
  TrendingDown,
  Layers,
  SearchX,
} from 'lucide-react';
import {useEffect} from 'react';
import {useToast} from '@/hooks/use-toast';
import {Separator} from './ui/separator';

const initialState: FormState = {
  status: 'idle',
  message: '',
  data: null,
};

function SubmitButton() {
  const {pending} = useFormStatus();
  return (
    <Button type="submit" disabled={pending} className="w-full">
      {pending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
      Generate Report
    </Button>
  );
}

export function TokenAnalysis() {
  const [state, formAction, isPending] = useActionState(
    getTokenReportAction,
    initialState
  );
  const {toast} = useToast();

  useEffect(() => {
    if (state.status === 'error' && state.message) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: state.message,
      });
    }
  }, [state, toast]);

  return (
    <div className="grid md:grid-cols-3 gap-8">
      <div className="md:col-span-1">
        <Card>
          <form action={formAction}>
            <CardHeader>
              <CardTitle>Agentic Token Analysis</CardTitle>
              <CardDescription>
                Enter a Solana token address for advanced rug pull detection and price forecasting.
                Powered by multi-agent AI system with real-time alerts.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="tokenAddress">Token Address (Required)</Label>
                <Input
                  id="tokenAddress"
                  name="tokenAddress"
                  placeholder="e.g., 6qHtAv...pump"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tokenName">Token Name (Optional)</Label>
                <Input
                  id="tokenName"
                  name="tokenName"
                  placeholder="e.g., CryptoPulse"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tokenTicker">Token Ticker (Optional)</Label>
                <Input
                  id="tokenTicker"
                  name="tokenTicker"
                  placeholder="e.g., PULSE"
                />
              </div>
            </CardContent>
            <CardFooter>
              <SubmitButton />
            </CardFooter>
          </form>
        </Card>
      </div>

      <div className="md:col-span-2">
        {isPending && (
          <Card className="h-full flex items-center justify-center">
            <CardContent className="text-center text-muted-foreground p-6">
              <Loader2 className="mx-auto h-12 w-12 mb-4 animate-spin" />
              <p>Generating report...</p>
              <p className="text-xs mt-2">This may take up to 30 seconds.</p>
            </CardContent>
          </Card>
        )}

        {!isPending && state.status === 'idle' && (
          <Card className="h-full flex items-center justify-center">
            <CardContent className="text-center text-muted-foreground p-6">
              <BotMessageSquare className="mx-auto h-12 w-12 mb-4" />
              <p>Your generated report will appear here.</p>
            </CardContent>
          </Card>
        )}

        {!isPending && state.status === 'error' && (
          <Card className="h-full flex items-center justify-center">
            <CardContent className="text-center text-destructive p-6">
              <SearchX className="mx-auto h-12 w-12 mb-4" />
              <p className="font-semibold">Analysis Failed</p>
              <p className="text-sm mt-2">{state.message}</p>
            </CardContent>
          </Card>
        )}

        {!isPending && state.status === 'success' && state.data && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>
                  Analysis Report for {state.data.tokenName} (
                  {state.data.tokenSymbol})
                </span>
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 rounded text-xs font-semibold ${
                    state.data.alertLevel === 'CRITICAL' ? 'bg-red-100 text-red-800' :
                    state.data.alertLevel === 'HIGH' ? 'bg-orange-100 text-orange-800' :
                    state.data.alertLevel === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {state.data.alertLevel} RISK
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {state.data.riskScore?.toFixed(1)}/10
                  </span>
                </div>
              </CardTitle>
              <CardDescription>
                TokenSentinel multi-agent analysis with {(state.data.confidence * 100).toFixed(0)}% confidence
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="font-headline text-lg font-semibold mb-2">
                  Key Information
                </h3>
                <p className="text-muted-foreground whitespace-pre-wrap">
                  {state.data.keyInformation}
                </p>
              </div>

              <Separator />

              <div className="border rounded-lg p-4 bg-background">
                <h3 className="font-headline text-lg font-semibold mb-2 flex items-center gap-2">
                  <Layers className="text-blue-400 w-5 h-5" /> On-Chain
                  Analysis
                </h3>
                <p className="text-muted-foreground whitespace-pre-wrap">
                  {state.data.onChainAnalysis}
                </p>
              </div>

              <Separator />

              <div>
                <h3 className="font-headline text-lg font-semibold mb-4">
                  Scenario Analysis
                </h3>
                <div className="grid gap-6 sm:grid-cols-2">
                  <div className="border rounded-lg p-4 bg-background/50">
                    <h4 className="font-semibold mb-2 flex items-center gap-2 text-green-400">
                      <TrendingUp className="w-5 h-5" /> Bullish Scenario
                    </h4>
                    <p className="text-muted-foreground whitespace-pre-wrap text-sm">
                      {state.data.scenarioAnalysis.bullish}
                    </p>
                  </div>
                  <div className="border rounded-lg p-4 bg-background/50">
                    <h4 className="font-semibold mb-2 flex items-center gap-2 text-red-400">
                      <TrendingDown className="w-5 h-5" /> Bearish Scenario
                    </h4>
                    <p className="text-muted-foreground whitespace-pre-wrap text-sm">
                      {state.data.scenarioAnalysis.bearish}
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="border rounded-lg p-4 bg-background">
                <h3 className="font-headline text-lg font-semibold mb-4 flex items-center gap-2">
                  <AlertTriangle className="text-yellow-500 w-5 h-5" /> Risk
                  Assessment
                </h3>

                {/* Risk Score Visualization */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Risk Score</span>
                    <span className="text-sm font-bold">{state.data.riskScore?.toFixed(1)}/10</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        (state.data.riskScore || 0) >= 8 ? 'bg-red-500' :
                        (state.data.riskScore || 0) >= 6 ? 'bg-orange-500' :
                        (state.data.riskScore || 0) >= 4 ? 'bg-yellow-500' :
                        'bg-green-500'
                      }`}
                      style={{ width: `${((state.data.riskScore || 0) / 10) * 100}%` }}
                    />
                  </div>
                </div>

                {/* Rug Pull Probability */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Rug Pull Probability</span>
                    <span className="text-sm font-bold">{((state.data.rugPullProbability || 0) * 100).toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full bg-red-500 transition-all duration-300"
                      style={{ width: `${((state.data.rugPullProbability || 0)) * 100}%` }}
                    />
                  </div>
                </div>

                {/* Price Direction */}
                <div className="mb-4 flex items-center gap-2">
                  <span className="text-sm font-medium">Price Direction:</span>
                  <span className={`px-2 py-1 rounded text-xs font-semibold ${
                    state.data.priceDirection === 'UP' ? 'bg-green-100 text-green-800' :
                    state.data.priceDirection === 'DOWN' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {state.data.priceDirection === 'UP' ? '📈 UP' :
                     state.data.priceDirection === 'DOWN' ? '📉 DOWN' :
                     '➡️ SIDEWAYS'}
                  </span>
                </div>

                <p className="text-muted-foreground whitespace-pre-wrap text-sm">
                  {state.data.riskAssessment}
                </p>
              </div>

              <Separator />

              <div>
                <h3 className="font-headline text-lg font-semibold mb-2">
                  Overall Assessment
                </h3>
                <p className="text-muted-foreground whitespace-pre-wrap">
                  {state.data.overallAssessment}
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
