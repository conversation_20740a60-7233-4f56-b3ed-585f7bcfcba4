
// THIS IS A WORK-IN-PROGRESS THAT CONSTANTLY STREAMS DATA FROM KRAKEN
"use client";

import { useEffect, useState, useMemo } from "react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from "@/components/ui/chart";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "./ui/skeleton";
import { useWebSocket } from "@/hooks/use-websocket.tsx";
import type { KrakenTicker } from "@/lib/types";
import { TOKEN_MAP } from "@/lib/kraken-pairs";

type ChartDataPoint = {
  date: string;
  price: number;
};

const chartConfig = {
  price: {
    label: "Price (USD)",
    color: "hsl(var(--accent))",
  },
} satisfies ChartConfig;

export function DashboardChart() {
  const [activePair, setActivePair] = useState("XBT/USD");
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [availablePairs, setAvailablePairs] = useState<string[]>(['XBT/USD']);

  const { lastMessage, isConnected } = useWebSocket('kraken');
  
  useEffect(() => {
    const fetchAssetPairs = async () => {
      try {
        const response = await fetch('https://api.kraken.com/0/public/AssetPairs');
        const data = await response.json();
        if (data.error && data.error.length > 0) {
          console.error("Error fetching asset pairs for chart:", data.error);
          return;
        }

        const result = data.result;
        const wsPairs = Object.values(result)
          .filter((pair: any) => pair.quote === 'ZUSD' && pair.wsname)
          .map((pair: any) => pair.wsname)
          .sort(); // Sort them alphabetically
        setAvailablePairs(wsPairs);
      } catch (error) {
        console.error("Failed to fetch asset pairs for chart:", error);
      }
    };
    fetchAssetPairs();
  }, [])

  useEffect(() => {
    if (isConnected && chartData.length > 0) {
        setIsLoading(false);
    }
  }, [isConnected, chartData]);

  useEffect(() => {
    const fetchHistoricalData = async () => {
        if (!activePair) return;
        setIsLoading(true);
        setChartData([]);
        try {
            const krakenPair = activePair.replace('/', '');
            const response = await fetch(`https://api.kraken.com/0/public/OHLC?pair=${krakenPair}&interval=60`);
            const data = await response.json();

            if (data.error && data.error.length > 0) {
                console.error("Error fetching historical data from Kraken:", data.error);
                setChartData([]);
                return;
            }
            const resultKey = Object.keys(data.result)[0];
            const ohlc = data.result[resultKey];

            const formattedData: ChartDataPoint[] = ohlc.slice(-100).map((d: any) => ({
                date: new Date(d[0] * 1000).toISOString(),
                price: parseFloat(d[4]), // closing price
            }));
            setChartData(formattedData);
        } catch (error) {
            console.error("Failed to fetch historical data:", error);
            setChartData([]);
        } finally {
            setIsLoading(false);
        }
    };

    fetchHistoricalData();
  }, [activePair]);

  useEffect(() => {
    if (lastMessage && Array.isArray(lastMessage) && lastMessage[2] === 'ticker' && lastMessage[3] === activePair) {
      const ticker = lastMessage[1] as KrakenTicker;
      const newPrice = parseFloat(ticker.c[0]);
      setChartData(prevData => {
          if (prevData.length === 0) return [{ date: new Date().toISOString(), price: newPrice }];
          const newData = [...prevData, { date: new Date().toISOString(), price: newPrice }];
          return newData.slice(-100); // Keep the last 100 points
      });
    }
  }, [lastMessage, activePair]);

  const yDomain = useMemo(() => {
    if (chartData.length === 0) return [0, 100];
    const prices = chartData.map(d => d.price);
    const min = Math.min(...prices);
    const max = Math.max(...prices);
    const padding = (max - min) * 0.1;
    return [min - padding, max + padding];
  }, [chartData]);

  const displayTokenName = (pair: string) => TOKEN_MAP[pair]?.name || pair.split('/')[0];

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
            <CardTitle>{displayTokenName(activePair)} Performance</CardTitle>
            <CardDescription>Live data from Kraken</CardDescription>
        </div>
        <Select value={activePair} onValueChange={setActivePair}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select Token" />
          </SelectTrigger>
          <SelectContent>
            {availablePairs.map(pair => (
              <SelectItem key={pair} value={pair}>{displayTokenName(pair)}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        {isLoading || chartData.length === 0 ? (
            <div className="h-[250px] w-full flex items-center justify-center">
                <Skeleton className="h-full w-full" />
            </div>
        ) : (
            <ChartContainer config={chartConfig} className="h-[250px] w-full">
            <AreaChart
                data={chartData}
                margin={{
                left: 12,
                right: 12,
                }}
            >
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis
                dataKey="date"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickFormatter={(value) => new Date(value).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}
                />
                <YAxis
                domain={yDomain}
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickFormatter={(value) => `$${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2})}`}
                />
                <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="dot" />} />
                <defs>
                    <linearGradient id="fillPrice" x1="0" y1="0" x2="0" y2="1">
                    <stop
                        offset="5%"
                        stopColor="hsl(var(--accent))"
                        stopOpacity={0.8}
                    />
                    <stop
                        offset="95%"
                        stopColor="hsl(var(--accent))"
                        stopOpacity={0.1}
                    />
                    </linearGradient>
                </defs>
                <Area
                dataKey="price"
                type="natural"
                fill="url(#fillPrice)"
                stroke="hsl(var(--accent))"
                stackId="a"
                isAnimationActive={false}
                />
            </AreaChart>
            </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
