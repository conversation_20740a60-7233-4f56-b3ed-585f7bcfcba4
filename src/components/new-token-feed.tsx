// This component establishes a WebSocket connection to pumpportal.fun
// to stream newly created tokens in real-time.
"use client";

import { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import Image from "next/image";
import { Button } from "./ui/button";
import { ExternalLink } from "lucide-react";
import { useWebSocket } from "@/hooks/use-websocket";
import type { NewTokenEvent } from "@/lib/types";

const MAX_TOKENS_DISPLAY = 50;

export function NewTokenFeed() {
  const [newTokens, setNewTokens] = useState<NewTokenEvent[]>([]);
  const { lastMessage, isConnected, subscribe } = useWebSocket('pumpfun');

  useEffect(() => {
    subscribe({ method: "subscribeNewToken" });
  }, [subscribe]);

  useEffect(() => {
    if (lastMessage && lastMessage.txType === 'create') {
        // Transform PumpPortal message to NewTokenEvent format
        const token: NewTokenEvent = {
            type: 'newToken',
            mint: lastMessage.mint,
            tx: lastMessage.signature,
            timestamp: Date.now(), // PumpPortal doesn't provide timestamp
            user: lastMessage.traderPublicKey,
            name: lastMessage.name,
            symbol: lastMessage.symbol,
            description: '', // Not provided by PumpPortal
            image: lastMessage.uri || '/placeholder-token.png',
            twitter: null,
            telegram: null,
            website: null,
            market_cap: lastMessage.marketCapSol * 150, // Approximate SOL to USD conversion
            creator_bal: lastMessage.initialBuy || 0,
        };

        setNewTokens(prevTokens => {
            const updatedTokens = [token, ...prevTokens];
            if (updatedTokens.length > MAX_TOKENS_DISPLAY) {
                return updatedTokens.slice(0, MAX_TOKENS_DISPLAY);
            }
            return updatedTokens;
        });
    }
  }, [lastMessage]);


  return (
    <Card>
        <CardHeader>
            <CardTitle className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
                <span>Live Feed</span>
            </CardTitle>
            <CardDescription>
                {isConnected ? "Streaming live token creation events from pumpportal.fun." : "Connecting to feed..."}
            </CardDescription>
        </CardHeader>
        <CardContent>
            <div className="rounded-lg border h-[600px] overflow-y-auto">
                <Table>
                    <TableHeader className="sticky top-0 bg-card">
                        <TableRow>
                            <TableHead>Token</TableHead>
                            <TableHead className="hidden md:table-cell">Market Cap</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {!isConnected && newTokens.length === 0 ? (
                            Array.from({ length: 10 }).map((_, i) => (
                                <TableRow key={i}>
                                    <TableCell><Skeleton className="h-8 w-48" /></TableCell>
                                    <TableCell className="hidden md:table-cell"><Skeleton className="h-5 w-24" /></TableCell>
                                    <TableCell className="text-right"><Skeleton className="h-8 w-20 float-right" /></TableCell>
                                </TableRow>
                            ))
                        ) : (
                            newTokens.map((token) => (
                                <TableRow key={token.mint} className="animate-in fade-in-0">
                                    <TableCell className="font-medium">
                                        <div className="flex items-center gap-3">
                                            <Image src={token.image} alt={token.name} width={40} height={40} className="rounded-full" unoptimized />
                                            <div className="flex flex-col">
                                                <span className="font-bold">{token.name}</span>
                                                <span className="text-xs text-muted-foreground">${token.symbol}</span>
                                            </div>
                                        </div>
                                    </TableCell>
                                    <TableCell className="hidden md:table-cell font-mono">
                                        ${token.market_cap.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2})}
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <Button variant="outline" size="sm" asChild>
                                            <a href={`https://raydium.io/swap/?ammId=${token.mint}`} target="_blank" rel="noopener noreferrer">
                                                Raydium <ExternalLink className="ml-2 h-3 w-3" />
                                            </a>
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))
                        )}
                         {isConnected && newTokens.length === 0 && (
                            <TableRow>
                                <TableCell colSpan={3} className="text-center text-muted-foreground py-10">
                                    Waiting for new tokens...
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
        </CardContent>
    </Card>
  );
}