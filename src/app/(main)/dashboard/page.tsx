import { Dashboard<PERSON><PERSON> } from "@/components/dashboard-chart";
import { MarketFeed } from "@/components/market-feed";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function DashboardPage() {
  return (
    <div className="flex flex-col gap-6">
      <h1 className="text-3xl font-headline font-bold">Market Pulse</h1>
      <DashboardChart />
      <Card>
        <CardHeader>
          <CardTitle>Trending Pairs</CardTitle>
          <CardDescription>
            Live market data for popular cryptocurrency pairs.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MarketFeed />
        </CardContent>
      </Card>
    </div>
  );
}
