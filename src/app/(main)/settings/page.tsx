import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

export default function SettingsPage() {
  return (
    <div className="flex flex-col gap-6">
      <h1 className="text-3xl font-headline font-bold">Settings</h1>
      <Card>
        <CardHeader>
          <CardTitle>API Key Management</CardTitle>
          <CardDescription>
            Manage your API keys for market data providers. Your keys are stored securely in your environment file.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <h3 className="font-medium">Kraken API</h3>
            <div className="space-y-2">
              <Label htmlFor="kraken-api-key">API Key</Label>
              <Input id="kraken-api-key" value={process.env.KRAKEN_API_KEY ? `${process.env.KRAKEN_API_KEY.substring(0, 8)}...` : 'Not Set'} readOnly />
            </div>
            <div className="space-y-2">
              <Label htmlFor="kraken-private-key">Private Key</Label>
              <Input id="kraken-private-key" type="password" value="********************" readOnly />
            </div>
          </div>
           <Button disabled>Update Keys</Button>
        </CardContent>
      </Card>
    </div>
  );
}
