import { NextRequest, NextResponse } from 'next/server';
import { startGlobalMonitoring, getMonitoringStats, healthCheck } from '@/lib/monitoring/real-time-monitor';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Starting TokenSentinel real-time monitoring...');
    
    // Parse configuration from request body
    const body = await request.json().catch(() => ({}));
    
    const config = {
      enabled: true,
      minMarketCap: body.minMarketCap || 5000,
      maxTokenAge: body.maxTokenAge || 24, // hours
      batchSize: body.batchSize || 5,
      processingInterval: body.processingInterval || 30000, // 30 seconds
      alertThreshold: body.alertThreshold || 6,
      maxTokensPerHour: body.maxTokensPerHour || 50,
    };
    
    console.log('📋 Monitoring Configuration:', config);
    
    // Start monitoring with configuration
    await startGlobalMonitoring(config);
    
    // Get initial stats
    const stats = getMonitoringStats();
    const health = healthCheck();
    
    console.log('✅ Real-time monitoring started successfully');
    
    return NextResponse.json({
      success: true,
      message: 'TokenSentinel real-time monitoring started',
      config,
      stats,
      health,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('❌ Failed to start monitoring:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get current monitoring status
    const stats = getMonitoringStats();
    const health = healthCheck();
    
    return NextResponse.json({
      success: true,
      stats,
      health,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
