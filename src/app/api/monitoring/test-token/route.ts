import { NextRequest, NextResponse } from 'next/server';
import { analyzeTokenManually, addTokenToMonitoring } from '@/lib/monitoring/real-time-monitor';
import { executeTokenSentinel } from '@/ai/workflows/token-sentinel';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tokenAddress, priority = 'HIGH', method = 'manual' } = body;
    
    if (!tokenAddress) {
      return NextResponse.json({
        success: false,
        error: 'Token address is required',
      }, { status: 400 });
    }
    
    console.log(`🔍 Testing token analysis: ${tokenAddress} (${method})`);
    
    let result;
    
    if (method === 'manual') {
      // Manual analysis
      await analyzeTokenManually(tokenAddress, priority);
      
      // Get the actual result for response
      result = await executeTokenSentinel({
        tokenAddress,
        priority,
        skipSocialAnalysis: true, // Skip for testing
      });
      
    } else if (method === 'queue') {
      // Add to monitoring queue
      const mockTokenEvent = {
        mint: tokenAddress,
        name: 'Test Token',
        symbol: 'TEST',
        market_cap: 10000,
        timestamp: Date.now(),
        creator_bal: 0.1,
      };
      
      addTokenToMonitoring(mockTokenEvent);
      
      result = {
        message: 'Token added to monitoring queue',
        tokenAddress,
        queuePosition: 'Added to queue',
      };
    }
    
    console.log(`✅ Token analysis completed: ${result.alertLevel || 'QUEUED'}`);
    
    return NextResponse.json({
      success: true,
      method,
      tokenAddress,
      result,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('❌ Token analysis failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
