import { NextRequest, NextResponse } from 'next/server';
import { sendTokenAlert, sendDailySummary } from '@/lib/telegram-alerts';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type = 'alert', testData } = body;
    
    console.log(`📱 Testing Telegram ${type}...`);
    
    let result;
    
    if (type === 'alert') {
      // Test token alert
      const mockResult = testData || {
        tokenAddress: 'So11111111111111111111111111111111111111112',
        tokenName: 'Test Token Alert',
        tokenSymbol: 'TEST',
        overallRiskScore: 8.5,
        rugPullProbability: 0.85,
        alertLevel: 'HIGH',
        confidence: 0.9,
        priceDirection: 'DOWN',
        executionTime: 5000,
        agentsExecuted: ['token_hunter', 'contract_auditor'],
        errors: [],
        shouldAlert: true,
        alertMessage: 'Test alert from TokenSentinel',
        recommendedAction: 'AVOID - High risk detected',
        contractAuditorResult: {
          hiddenMintDetected: true,
          ownershipRenounced: false,
          blacklistFunction: true,
        },
        onChainAnalystResult: {
          holderConcentration: 0.9,
          liquidityUSD: 5000,
          suspiciousActivity: true,
        },
        socialSentimentResult: {
          botActivityDetected: true,
          mentionCount: 50,
          sentimentScore: -0.3,
        },
      };
      
      const alertSent = await sendTokenAlert(mockResult);
      
      result = {
        alertSent,
        telegramConfigured: !!process.env.TELEGRAM_BOT_TOKEN,
        mockData: mockResult,
      };
      
    } else if (type === 'summary') {
      // Test daily summary
      const mockStats = testData || {
        tokensAnalyzed: 150,
        rugPullsDetected: 12,
        highRiskTokens: 25,
        opportunitiesFound: 8,
        averageRiskScore: 5.2,
      };
      
      const summarySent = await sendDailySummary(mockStats);
      
      result = {
        summarySent,
        telegramConfigured: !!process.env.TELEGRAM_BOT_TOKEN,
        mockStats,
      };
    }
    
    console.log(`${result.alertSent || result.summarySent ? '✅' : '⚠️'} Telegram ${type} test completed`);
    
    return NextResponse.json({
      success: true,
      type,
      result,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error(`❌ Telegram ${body?.type || 'test'} failed:`, error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      telegramConfigured: !!process.env.TELEGRAM_BOT_TOKEN,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
