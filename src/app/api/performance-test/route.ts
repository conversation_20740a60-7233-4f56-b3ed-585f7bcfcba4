import { NextRequest, NextResponse } from 'next/server';
import { executeTokenSentinel, batchAnalyzeTokens } from '@/ai/workflows/token-sentinel';

// Test tokens for performance testing
const TEST_TOKENS = [
  'So11111111111111111111111111111111111111112', // Wrapped SOL
  '7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr', // POPCAT
  'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
  'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', // USDT
  'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So', // mSOL
];

interface PerformanceResult {
  tokenAddress: string;
  success: boolean;
  duration: number;
  riskScore?: number;
  alertLevel?: string;
  error?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      testType = 'sequential', 
      tokenCount = 5, 
      concurrency = 3,
      skipSocialAnalysis = true 
    } = body;
    
    console.log(`🚀 Starting performance test: ${testType} (${tokenCount} tokens)`);
    
    const startTime = Date.now();
    const results: PerformanceResult[] = [];
    
    // Select tokens for testing
    const tokensToTest = TEST_TOKENS.slice(0, Math.min(tokenCount, TEST_TOKENS.length));
    
    if (testType === 'sequential') {
      // Sequential processing
      for (const tokenAddress of tokensToTest) {
        const tokenStartTime = Date.now();
        
        try {
          const result = await executeTokenSentinel({
            tokenAddress,
            priority: 'MEDIUM',
            skipSocialAnalysis,
          });
          
          results.push({
            tokenAddress,
            success: true,
            duration: Date.now() - tokenStartTime,
            riskScore: result.overallRiskScore,
            alertLevel: result.alertLevel,
          });
          
        } catch (error) {
          results.push({
            tokenAddress,
            success: false,
            duration: Date.now() - tokenStartTime,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
        
        // Small delay between requests to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
    } else if (testType === 'batch') {
      // Batch processing
      const inputs = tokensToTest.map(tokenAddress => ({
        tokenAddress,
        priority: 'MEDIUM' as const,
        skipSocialAnalysis,
      }));
      
      const batchResults = await batchAnalyzeTokens(inputs);
      
      batchResults.forEach((result, index) => {
        results.push({
          tokenAddress: tokensToTest[index],
          success: result.errors.length === 0,
          duration: result.executionTime,
          riskScore: result.overallRiskScore,
          alertLevel: result.alertLevel,
          error: result.errors.length > 0 ? result.errors[0] : undefined,
        });
      });
      
    } else if (testType === 'concurrent') {
      // Concurrent processing with limited concurrency
      const promises = tokensToTest.map(async (tokenAddress) => {
        const tokenStartTime = Date.now();
        
        try {
          const result = await executeTokenSentinel({
            tokenAddress,
            priority: 'LOW',
            skipSocialAnalysis,
          });
          
          return {
            tokenAddress,
            success: true,
            duration: Date.now() - tokenStartTime,
            riskScore: result.overallRiskScore,
            alertLevel: result.alertLevel,
          };
          
        } catch (error) {
          return {
            tokenAddress,
            success: false,
            duration: Date.now() - tokenStartTime,
            error: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      });
      
      // Process with limited concurrency
      for (let i = 0; i < promises.length; i += concurrency) {
        const batch = promises.slice(i, i + concurrency);
        const batchResults = await Promise.all(batch);
        results.push(...batchResults);
        
        // Small delay between batches
        if (i + concurrency < promises.length) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }
    
    const totalDuration = Date.now() - startTime;
    
    // Calculate performance metrics
    const successfulTests = results.filter(r => r.success);
    const failedTests = results.filter(r => !r.success);
    
    const avgDuration = successfulTests.length > 0 ? 
      successfulTests.reduce((sum, r) => sum + r.duration, 0) / successfulTests.length : 0;
    
    const maxDuration = Math.max(...results.map(r => r.duration));
    const minDuration = Math.min(...results.map(r => r.duration));
    
    const throughputPerMinute = (successfulTests.length / totalDuration) * 60000;
    
    const summary = {
      testType,
      totalTokens: results.length,
      successful: successfulTests.length,
      failed: failedTests.length,
      successRate: (successfulTests.length / results.length) * 100,
      totalDuration,
      avgDuration: Math.round(avgDuration),
      maxDuration,
      minDuration,
      throughputPerMinute: Math.round(throughputPerMinute * 100) / 100,
    };
    
    console.log(`✅ Performance test completed:`);
    console.log(`   Success Rate: ${summary.successRate.toFixed(1)}%`);
    console.log(`   Avg Duration: ${summary.avgDuration}ms`);
    console.log(`   Throughput: ${summary.throughputPerMinute} tokens/minute`);
    
    return NextResponse.json({
      success: true,
      summary,
      results,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('❌ Performance test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
