import { NextRequest, NextResponse } from 'next/server';
import { executeTokenSentinel } from '@/ai/workflows/token-sentinel';
import { generateTokenSentinelReport } from '@/ai/flows/token-sentinel-flow';
import { executeTokenHunter } from '@/ai/agents/token-hunter';
import { executeContractAuditor } from '@/ai/agents/contract-auditor';
import { executeOnChainAnalyst } from '@/ai/agents/onchain-analyst';

// Test configuration
const TEST_TOKENS = {
  SAFE_TOKEN: 'So11111111111111111111111111111111111111112', // Wrapped SOL
  RISKY_TOKEN: '7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr', // Example token
  INVALID_TOKEN: 'invalid_address_for_testing',
};

interface TestResult {
  name: string;
  passed: boolean;
  duration: number;
  result?: any;
  error?: string;
}

async function runTest(testName: string, testFunction: () => Promise<any>): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    console.log(`🧪 Running: ${testName}`);
    const result = await testFunction();
    const duration = Date.now() - startTime;
    
    console.log(`✅ ${testName} - PASSED (${duration}ms)`);
    return { name: testName, passed: true, duration, result };
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ ${testName} - FAILED (${duration}ms): ${errorMessage}`);
    return { name: testName, passed: false, duration, error: errorMessage };
  }
}

// Test 1: API Connectivity
async function testAPIConnectivity() {
  const requiredEnvVars = [
    'OPENROUTER_API_KEY',
    'API_BIRDEYE_API_KEY',
    'ETHERSCAN_API_KEY'
  ];
  
  const missing = requiredEnvVars.filter(key => !process.env[key]);
  if (missing.length > 0) {
    throw new Error(`Missing environment variables: ${missing.join(', ')}`);
  }
  
  // Test OpenRouter connectivity
  const response = await fetch('https://openrouter.ai/api/v1/models', {
    headers: {
      'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
    },
  });
  
  if (!response.ok) {
    throw new Error(`OpenRouter API error: ${response.status}`);
  }
  
  return { apisConfigured: requiredEnvVars.length, openRouterStatus: 'connected' };
}

// Test 2: Token Hunter Agent
async function testTokenHunter() {
  const state = {
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    tokenName: 'Wrapped SOL',
    tokenSymbol: 'WSOL',
    errors: [],
    completed: false,
  };
  
  const result = await executeTokenHunter(state);
  
  if (!result.tokenHunterResult) {
    throw new Error('Token Hunter did not return result');
  }
  
  return {
    tokenFound: !!result.tokenHunterResult,
    marketCap: result.tokenHunterResult.marketCap,
    source: result.tokenHunterResult.source,
  };
}

// Test 3: Contract Auditor Agent
async function testContractAuditor() {
  const state = {
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    tokenName: 'Wrapped SOL',
    errors: [],
    completed: false,
  };
  
  const result = await executeContractAuditor(state);
  
  if (!result.contractAuditorResult) {
    throw new Error('Contract Auditor did not return result');
  }
  
  return {
    riskScore: result.contractAuditorResult.riskScore,
    confidence: result.contractAuditorResult.confidence,
    contractVerified: result.contractAuditorResult.contractVerified,
  };
}

// Test 4: On-Chain Analyst Agent
async function testOnChainAnalyst() {
  const state = {
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    tokenName: 'Wrapped SOL',
    errors: [],
    completed: false,
  };
  
  const result = await executeOnChainAnalyst(state);
  
  if (!result.onChainAnalystResult) {
    throw new Error('On-Chain Analyst did not return result');
  }
  
  return {
    liquidityUSD: result.onChainAnalystResult.liquidityUSD,
    holderConcentration: result.onChainAnalystResult.holderConcentration,
    suspiciousActivity: result.onChainAnalystResult.suspiciousActivity,
  };
}

// Test 5: Full TokenSentinel Workflow
async function testFullWorkflow() {
  const input = {
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    tokenName: 'Wrapped SOL',
    tokenSymbol: 'WSOL',
    priority: 'HIGH' as const,
    skipSocialAnalysis: true, // Skip to avoid rate limits
  };
  
  const result = await executeTokenSentinel(input);
  
  if (!result.overallRiskScore || !result.alertLevel) {
    throw new Error('TokenSentinel workflow incomplete');
  }
  
  // Validate risk score range
  if (result.overallRiskScore < 0 || result.overallRiskScore > 10) {
    throw new Error('Risk score out of range');
  }
  
  return {
    riskScore: result.overallRiskScore,
    alertLevel: result.alertLevel,
    confidence: result.confidence,
    executionTime: result.executionTime,
    agentsExecuted: result.agentsExecuted.length,
  };
}

// Test 6: UI Integration
async function testUIIntegration() {
  const input = {
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    tokenName: 'Wrapped SOL',
    tokenTicker: 'WSOL',
  };
  
  const result = await generateTokenSentinelReport(input);
  
  const requiredFields = [
    'keyInformation',
    'onChainAnalysis',
    'riskAssessment',
    'scenarioAnalysis',
    'overallAssessment',
  ];
  
  for (const field of requiredFields) {
    if (!result[field]) {
      throw new Error(`Missing required field: ${field}`);
    }
  }
  
  return {
    fieldsPresent: requiredFields.length,
    riskScore: result.riskScore,
    alertLevel: result.alertLevel,
  };
}

// Test 7: Error Handling
async function testErrorHandling() {
  const input = {
    tokenAddress: TEST_TOKENS.INVALID_TOKEN,
    priority: 'LOW' as const,
    skipSocialAnalysis: true,
  };
  
  const result = await executeTokenSentinel(input);
  
  // Should handle error gracefully
  if (result.errors.length === 0) {
    throw new Error('Expected errors for invalid token address');
  }
  
  // Should still return valid structure
  if (!result.alertLevel || typeof result.overallRiskScore !== 'number') {
    throw new Error('Error handling did not return valid structure');
  }
  
  return {
    errorsHandled: result.errors.length,
    gracefulDegradation: true,
    finalRiskScore: result.overallRiskScore,
  };
}

export async function GET(request: NextRequest) {
  console.log('🚀 TokenSentinel Integration Tests');
  console.log('==================================\n');
  
  const startTime = Date.now();
  const results: TestResult[] = [];
  
  try {
    // Run all tests
    results.push(await runTest('API Connectivity', testAPIConnectivity));
    results.push(await runTest('Token Hunter Agent', testTokenHunter));
    results.push(await runTest('Contract Auditor Agent', testContractAuditor));
    results.push(await runTest('On-Chain Analyst Agent', testOnChainAnalyst));
    results.push(await runTest('Full TokenSentinel Workflow', testFullWorkflow));
    results.push(await runTest('UI Integration', testUIIntegration));
    results.push(await runTest('Error Handling', testErrorHandling));
    
    // Calculate summary
    const totalDuration = Date.now() - startTime;
    const passed = results.filter(r => r.passed).length;
    const failed = results.length - passed;
    const successRate = (passed / results.length) * 100;
    
    const summary = {
      total: results.length,
      passed,
      failed,
      successRate: parseFloat(successRate.toFixed(1)),
      totalDuration,
    };
    
    console.log('\n📊 Test Summary');
    console.log('===============');
    console.log(`Total Tests: ${summary.total}`);
    console.log(`Passed: ${summary.passed}`);
    console.log(`Failed: ${summary.failed}`);
    console.log(`Success Rate: ${summary.successRate}%`);
    console.log(`Total Duration: ${summary.totalDuration}ms`);
    
    // Log detailed results
    console.log('\n📋 Detailed Results');
    console.log('===================');
    results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.name} (${result.duration}ms)`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    return NextResponse.json({
      success: failed === 0,
      summary,
      results,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      results,
    }, { status: 500 });
  }
}
