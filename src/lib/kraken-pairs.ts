
// This file provides a manual mapping for well-known tokens to improve their display names.
// The actual list of tokens to subscribe to is now fetched dynamically from the Kraken API.
// This mapping helps translate WebSocket names (like XBT) to common names (like Bitcoin).

export const TOKEN_MAP: { [key: string]: { name: string; symbol: string } } = {
  'XBT/USD': { name: 'Bitcoin', symbol: 'BTC' },
  'ETH/USD': { name: 'Ethereum', symbol: 'ETH' },
  'USDT/USD': { name: 'Tether', symbol: 'USDT' },
  'SOL/USD': { name: 'Sol<PERSON>', symbol: 'SOL' },
  'XRP/USD': { name: 'Ripple', symbol: 'XRP' },
  'USDC/USD': { name: 'USD Coin', symbol: 'USDC' },
  'ADA/USD': { name: 'Cardano', symbol: 'ADA' },
  'AVAX/USD': { name: 'Avalanche', symbol: 'AVAX' },
  'DOGE/USD': { name: '<PERSON><PERSON><PERSON><PERSON>', symbol: '<PERSON><PERSON><PERSON>' },
  'DOT/USD': { name: '<PERSON><PERSON><PERSON>', symbol: 'DOT' },
  'LINK/USD': { name: 'Chainlink', symbol: 'LINK' },
  'MATIC/USD': { name: 'Polygon', symbol: 'MATIC' },
  'SHIB/USD': { name: 'Shiba Inu', symbol: 'SHIB' },
  'TRX/USD': { name: 'TRON', symbol: 'TRX' },
  'BCH/USD': { name: 'Bitcoin Cash', symbol: 'BCH' },
  'ICP/USD': { name: 'Internet Computer', symbol: 'ICP' },
  'LTC/USD': { name: 'Litecoin', symbol: 'LTC' },
  'NEAR/USD': { name: 'NEAR Protocol', symbol: 'NEAR' },
  'UNI/USD': { name: 'Uniswap', symbol: 'UNI' },
  'ETC/USD': { name: 'Ethereum Classic', symbol: 'ETC' },
  'ATOM/USD': { name: 'Cosmos', symbol: 'ATOM' },
  'INJ/USD': { name: 'Injective', symbol: 'INJ' },
  'XLM/USD': { name: 'Stellar', symbol: 'XLM' },
  'FIL/USD': { name: 'Filecoin', symbol: 'FIL' },
  'HBAR/USD': { name: 'Hedera', symbol: 'HBAR' },
  'APT/USD': { name: 'Aptos', symbol: 'APT' },
  'CRO/USD': { name: 'Cronos', symbol: 'CRO' },
  'TIA/USD': { name: 'Celestia', symbol: 'TIA' },
  'IMX/USD': { name: 'Immutable', symbol: 'IMX' },
  'VET/USD': { name: 'VeChain', symbol: 'VET' },
  'GRT/USD': { name: 'The Graph', symbol: 'GRT' },
  'RNDR/USD': { name: 'Render Token', symbol: 'RNDR' },
  'OP/USD': { name: 'Optimism', symbol: 'OP' },
  'AR/USD': { name: 'Arweave', symbol: 'AR' },
  'MKR/USD': { name: 'Maker', symbol: 'MKR' },
  'AAVE/USD': { name: 'Aave', symbol: 'AAVE' },
  'ALGO/USD': { name: 'Algorand', symbol: 'ALGO' },
  'EGLD/USD': { name: 'MultiversX', symbol: 'EGLD' },
  'STX/USD': { name: 'Stacks', symbol: 'STX' },
  'SAND/USD': { name: 'The Sandbox', symbol: 'SAND' },
  'MANA/USD': { name: 'Decentraland', symbol: 'MANA' },
  'AXS/USD': { name: 'Axie Infinity', symbol: 'AXS' },
  'FET/USD': { name: 'Fetch.ai', symbol: 'FET' },
  'XTZ/USD': { name: 'Tezos', symbol: 'XTZ' },
  'EOS/USD': { name: 'EOS', symbol: 'EOS' },
  'SNX/USD': { name: 'Synthetix', symbol: 'SNX' },
  'FLOW/USD': { name: 'Flow', symbol: 'FLOW' },
  'APE/USD': { name: 'ApeCoin', symbol: 'APE' },
  'LDO/USD': { name: 'Lido DAO', symbol: 'LDO' },
  'SUI/USD': { name: 'Sui', symbol: 'SUI' },
};

// Kept for watchlist, but the main market feed now uses a dynamic list.
export const ALL_PAIRS = Object.keys(TOKEN_MAP);
