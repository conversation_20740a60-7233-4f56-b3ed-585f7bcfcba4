// https://docs.pumpportal.fun/websocket-api/subscribe-new-token
export type NewTokenEvent = {
    type: 'newToken';
    mint: string;
    tx: string;
    timestamp: number;
    user: string;
    name: string;
    symbol: string;
    description: string;
    image: string;
    twitter: string | null;
    telegram: string | null;
    website: string | null;
    market_cap: number;
    creator_bal: number;
};

// https://docs.kraken.com/websockets/#message-ticker
export type KrakenTicker = {
    a: [string, number, string]; // Ask [<price>, <whole lot volume>, <lot volume>]
    b: [string, number, string]; // Bid [<price>, <whole lot volume>, <lot volume>]
    c: [string, string];          // Last trade closed [<price>, <lot volume>]
    v: [string, string];          // Volume [<today>, <last 24 hours>]
    p: [string, string];          // Volume weighted average price [<today>, <last 24 hours>]
    t: [number, number];          // Number of trades [<today>, <last 24 hours>]
    l: [string, string];          // Low [<today>, <last 24 hours>]
    h: [string, string];          // High [<today>, <last 24 hours>]
    o: [string, string];          // Today's opening price
};
