/**
 * @fileOverview Real-time Token Monitoring Service
 * Continuously monitors new token launches and automatically runs TokenSentinel analysis
 */

import { z } from 'zod';
import { executeTokenSentinel, batchAnalyzeTokens, type TokenSentinelInput } from '../../ai/workflows/token-sentinel';
import { sendTokenAlert, sendBatchAlerts, sendDailySummary } from '../telegram-alerts';
import type { NewTokenEvent } from '../types';

// Monitoring configuration
export const MonitoringConfig = z.object({
  enabled: z.boolean().default(true),
  minMarketCap: z.number().default(1000),
  maxTokenAge: z.number().default(24), // hours
  batchSize: z.number().default(10),
  processingInterval: z.number().default(30000), // 30 seconds
  alertThreshold: z.number().default(6), // minimum risk score for alerts
  maxTokensPerHour: z.number().default(100),
});

export type MonitoringConfig = z.infer<typeof MonitoringConfig>;

// Monitoring statistics
interface MonitoringStats {
  tokensDetected: number;
  tokensAnalyzed: number;
  rugPullsDetected: number;
  alertsSent: number;
  errors: number;
  startTime: number;
  lastProcessedTime: number;
}

class RealTimeMonitor {
  private config: MonitoringConfig;
  private stats: MonitoringStats;
  private tokenQueue: NewTokenEvent[] = [];
  private processingQueue: TokenSentinelInput[] = [];
  private isProcessing = false;
  private intervalId: NodeJS.Timeout | null = null;

  constructor(config: Partial<MonitoringConfig> = {}) {
    this.config = MonitoringConfig.parse(config);
    this.stats = {
      tokensDetected: 0,
      tokensAnalyzed: 0,
      rugPullsDetected: 0,
      alertsSent: 0,
      errors: 0,
      startTime: Date.now(),
      lastProcessedTime: Date.now(),
    };
  }

  /**
   * Start real-time monitoring
   */
  async start(): Promise<void> {
    if (!this.config.enabled) {
      console.log('[RealTimeMonitor] Monitoring is disabled');
      return;
    }

    console.log('[RealTimeMonitor] Starting real-time token monitoring...');
    
    // Start processing interval
    this.intervalId = setInterval(() => {
      this.processTokenQueue().catch(error => {
        console.error('[RealTimeMonitor] Processing error:', error);
        this.stats.errors++;
      });
    }, this.config.processingInterval);

    // Setup daily summary
    this.scheduleDailySummary();

    console.log('[RealTimeMonitor] Real-time monitoring started');
  }

  /**
   * Stop monitoring
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    console.log('[RealTimeMonitor] Monitoring stopped');
  }

  /**
   * Add new token to monitoring queue
   */
  addToken(token: NewTokenEvent): void {
    // Filter tokens based on criteria
    if (!this.shouldMonitorToken(token)) {
      return;
    }

    this.tokenQueue.push(token);
    this.stats.tokensDetected++;

    console.log(`[RealTimeMonitor] Added token to queue: ${token.name} (${token.symbol})`);
  }

  /**
   * Process token queue
   */
  private async processTokenQueue(): Promise<void> {
    if (this.isProcessing || this.tokenQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // Take batch of tokens from queue
      const batch = this.tokenQueue.splice(0, this.config.batchSize);
      
      // Convert to TokenSentinel input format
      const inputs: TokenSentinelInput[] = batch.map(token => ({
        tokenAddress: token.mint,
        tokenName: token.name,
        tokenSymbol: token.symbol,
        priority: this.determinePriority(token),
        skipSocialAnalysis: false,
      }));

      console.log(`[RealTimeMonitor] Processing batch of ${inputs.length} tokens`);

      // Analyze tokens in parallel
      const results = await batchAnalyzeTokens(inputs);

      // Process results
      for (const result of results) {
        this.stats.tokensAnalyzed++;

        // Check if it's a rug pull
        if (result.rugPullProbability > 0.7) {
          this.stats.rugPullsDetected++;
        }

        // Send alert if warranted
        if (result.shouldAlert && result.overallRiskScore >= this.config.alertThreshold) {
          const alertSent = await sendTokenAlert(result);
          if (alertSent) {
            this.stats.alertsSent++;
          }
        }
      }

      this.stats.lastProcessedTime = Date.now();

      console.log(`[RealTimeMonitor] Processed ${results.length} tokens, ${this.stats.alertsSent} alerts sent`);

    } catch (error) {
      console.error('[RealTimeMonitor] Batch processing error:', error);
      this.stats.errors++;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Determine if token should be monitored
   */
  private shouldMonitorToken(token: NewTokenEvent): boolean {
    // Check market cap threshold
    if (token.market_cap < this.config.minMarketCap) {
      return false;
    }

    // Check token age
    const ageHours = (Date.now() - token.timestamp) / (1000 * 60 * 60);
    if (ageHours > this.config.maxTokenAge) {
      return false;
    }

    // Rate limiting check
    const tokensThisHour = this.getTokensProcessedThisHour();
    if (tokensThisHour >= this.config.maxTokensPerHour) {
      return false;
    }

    return true;
  }

  /**
   * Determine processing priority
   */
  private determinePriority(token: NewTokenEvent): 'LOW' | 'MEDIUM' | 'HIGH' {
    // High priority for large market cap or high volume
    if (token.market_cap > 100000 || token.creator_bal < 0.1) {
      return 'HIGH';
    }

    // Medium priority for moderate metrics
    if (token.market_cap > 10000) {
      return 'MEDIUM';
    }

    return 'LOW';
  }

  /**
   * Get tokens processed in the last hour
   */
  private getTokensProcessedThisHour(): number {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    // This would track tokens processed in the last hour
    // For now, return a simple calculation
    return Math.floor(this.stats.tokensAnalyzed / Math.max(1, (Date.now() - this.stats.startTime) / (60 * 60 * 1000)));
  }

  /**
   * Schedule daily summary reports
   */
  private scheduleDailySummary(): void {
    // Send summary every 24 hours
    setInterval(async () => {
      try {
        const summary = this.generateDailySummary();
        await sendDailySummary(summary);
        console.log('[RealTimeMonitor] Daily summary sent');
      } catch (error) {
        console.error('[RealTimeMonitor] Failed to send daily summary:', error);
      }
    }, 24 * 60 * 60 * 1000); // 24 hours
  }

  /**
   * Generate daily summary statistics
   */
  private generateDailySummary(): {
    tokensAnalyzed: number;
    rugPullsDetected: number;
    highRiskTokens: number;
    opportunitiesFound: number;
    averageRiskScore: number;
  } {
    const runtimeHours = (Date.now() - this.stats.startTime) / (1000 * 60 * 60);
    
    return {
      tokensAnalyzed: this.stats.tokensAnalyzed,
      rugPullsDetected: this.stats.rugPullsDetected,
      highRiskTokens: Math.floor(this.stats.rugPullsDetected * 1.5), // Estimate
      opportunitiesFound: Math.floor(this.stats.tokensAnalyzed * 0.05), // Estimate 5%
      averageRiskScore: 5.5, // Would calculate from actual data
    };
  }

  /**
   * Get current monitoring statistics
   */
  getStats(): MonitoringStats & {
    uptime: number;
    tokensPerHour: number;
    queueSize: number;
  } {
    const uptime = Date.now() - this.stats.startTime;
    const tokensPerHour = this.stats.tokensAnalyzed / Math.max(1, uptime / (1000 * 60 * 60));

    return {
      ...this.stats,
      uptime,
      tokensPerHour,
      queueSize: this.tokenQueue.length,
    };
  }

  /**
   * Update monitoring configuration
   */
  updateConfig(newConfig: Partial<MonitoringConfig>): void {
    this.config = MonitoringConfig.parse({ ...this.config, ...newConfig });
    console.log('[RealTimeMonitor] Configuration updated:', this.config);
  }
}

// Global monitor instance
let globalMonitor: RealTimeMonitor | null = null;

/**
 * Get or create global monitor instance
 */
export function getMonitor(config?: Partial<MonitoringConfig>): RealTimeMonitor {
  if (!globalMonitor) {
    globalMonitor = new RealTimeMonitor(config);
  }
  return globalMonitor;
}

/**
 * Start global monitoring
 */
export async function startGlobalMonitoring(config?: Partial<MonitoringConfig>): Promise<void> {
  const monitor = getMonitor(config);
  await monitor.start();
}

/**
 * Stop global monitoring
 */
export function stopGlobalMonitoring(): void {
  if (globalMonitor) {
    globalMonitor.stop();
  }
}

/**
 * Add token to global monitoring queue
 */
export function addTokenToMonitoring(token: NewTokenEvent): void {
  const monitor = getMonitor();
  monitor.addToken(token);
}

/**
 * Get global monitoring statistics
 */
export function getMonitoringStats(): ReturnType<RealTimeMonitor['getStats']> | null {
  if (!globalMonitor) {
    return null;
  }
  return globalMonitor.getStats();
}

/**
 * Integration with existing WebSocket service
 */
export function integrateWithWebSocket(): void {
  // This would integrate with your existing WebSocket service
  // in src/hooks/use-websocket.tsx and src/lib/websocket-service.ts
  
  console.log('[RealTimeMonitor] WebSocket integration would be implemented here');
  
  // Example integration:
  // const { subscribe } = useWebSocket('pumpfun');
  // subscribe({ method: "subscribeNewToken" });
  // 
  // On new token event:
  // addTokenToMonitoring(newTokenEvent);
}

/**
 * Manual token analysis trigger
 */
export async function analyzeTokenManually(
  tokenAddress: string,
  priority: 'LOW' | 'MEDIUM' | 'HIGH' = 'HIGH'
): Promise<void> {
  try {
    console.log(`[RealTimeMonitor] Manual analysis requested for ${tokenAddress}`);
    
    const result = await executeTokenSentinel({
      tokenAddress,
      priority,
      skipSocialAnalysis: false,
    });
    
    // Send alert if warranted
    if (result.shouldAlert) {
      await sendTokenAlert(result);
    }
    
    console.log(`[RealTimeMonitor] Manual analysis completed: ${result.alertLevel} risk`);
    
  } catch (error) {
    console.error('[RealTimeMonitor] Manual analysis failed:', error);
    throw error;
  }
}

/**
 * Health check for monitoring system
 */
export function healthCheck(): {
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: {
    monitorRunning: boolean;
    queueSize: number;
    errorRate: number;
    lastProcessed: number;
  };
} {
  const stats = getMonitoringStats();
  
  if (!stats) {
    return {
      status: 'unhealthy',
      details: {
        monitorRunning: false,
        queueSize: 0,
        errorRate: 0,
        lastProcessed: 0,
      },
    };
  }
  
  const errorRate = stats.errors / Math.max(1, stats.tokensAnalyzed);
  const timeSinceLastProcessed = Date.now() - stats.lastProcessedTime;
  
  let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
  
  if (errorRate > 0.1 || timeSinceLastProcessed > 300000) { // 5 minutes
    status = 'degraded';
  }
  
  if (errorRate > 0.3 || timeSinceLastProcessed > 600000) { // 10 minutes
    status = 'unhealthy';
  }
  
  return {
    status,
    details: {
      monitorRunning: true,
      queueSize: stats.queueSize,
      errorRate,
      lastProcessed: timeSinceLastProcessed,
    },
  };
}
