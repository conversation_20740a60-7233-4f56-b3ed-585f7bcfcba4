/**
 * @fileOverview Multi-Source Data Fetcher - Uses all available APIs for maximum data coverage
 * Implements fallback strategies and data aggregation from multiple sources
 */

import { z } from 'zod';

// Comprehensive token data schema
export const TokenDataSchema = z.object({
  address: z.string(),
  name: z.string().optional(),
  symbol: z.string().optional(),
  
  // Market data
  price: z.number().optional(),
  marketCap: z.number().optional(),
  volume24h: z.number().optional(),
  
  // On-chain metrics
  totalSupply: z.number().optional(),
  circulatingSupply: z.number().optional(),
  holders: z.number().optional(),
  
  // Liquidity data
  liquidityUSD: z.number().optional(),
  liquidityChange24h: z.number().optional(),
  
  // Holder distribution
  topHolders: z.array(z.object({
    address: z.string(),
    balance: z.number(),
    percentage: z.number(),
  })).optional(),
  
  // Contract info
  contractVerified: z.boolean().optional(),
  creatorAddress: z.string().optional(),
  deploymentTime: z.string().optional(),
  
  // Metadata
  dataSource: z.string(),
  confidence: z.number().min(0).max(1),
  lastUpdated: z.string(),
});

export type TokenData = z.infer<typeof TokenDataSchema>;

// API configuration
const API_CONFIG = {
  DUNE: {
    baseUrl: 'https://api.dune.com/api/v1',
    apiKey: process.env.DUNE_API_KEY,
  },
  ALCHEMY: {
    baseUrl: process.env.WEB3_PROVIDER_URL,
  },
  ETHERSCAN: {
    baseUrl: 'https://api.etherscan.io/api',
    apiKey: process.env.ETHERSCAN_API_KEY,
  },
  BIRDEYE: {
    baseUrl: 'https://public-api.birdeye.so/defi',
    apiKey: process.env.API_BIRDEYE_API_KEY,
  },
  DEXSCREENER: {
    baseUrl: 'https://api.dexscreener.com/latest/dex',
  },
};

/**
 * Multi-source token data fetcher with intelligent fallbacks
 */
export class MultiSourceFetcher {
  private cache = new Map<string, { data: TokenData; expires: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Fetch comprehensive token data from multiple sources
   */
  async fetchTokenData(tokenAddress: string): Promise<TokenData> {
    // Check cache first
    const cached = this.cache.get(tokenAddress);
    if (cached && cached.expires > Date.now()) {
      return cached.data;
    }

    console.log(`[MultiSourceFetcher] Fetching data for ${tokenAddress}`);

    // Fetch from multiple sources in parallel
    const [
      duneData,
      alchemyData,
      birdeyeData,
      dexScreenerData,
      etherscanData,
    ] = await Promise.allSettled([
      this.fetchFromDune(tokenAddress),
      this.fetchFromAlchemy(tokenAddress),
      this.fetchFromBirdeye(tokenAddress),
      this.fetchFromDexScreener(tokenAddress),
      this.fetchFromEtherscan(tokenAddress),
    ]);

    // Aggregate data from successful sources
    const aggregatedData = this.aggregateData(tokenAddress, {
      dune: duneData.status === 'fulfilled' ? duneData.value : null,
      alchemy: alchemyData.status === 'fulfilled' ? alchemyData.value : null,
      birdeye: birdeyeData.status === 'fulfilled' ? birdeyeData.value : null,
      dexscreener: dexScreenerData.status === 'fulfilled' ? dexScreenerData.value : null,
      etherscan: etherscanData.status === 'fulfilled' ? etherscanData.value : null,
    });

    // Cache the result
    this.cache.set(tokenAddress, {
      data: aggregatedData,
      expires: Date.now() + this.CACHE_TTL,
    });

    return aggregatedData;
  }

  /**
   * Fetch data from Dune Analytics (professional blockchain analytics)
   */
  private async fetchFromDune(tokenAddress: string): Promise<any> {
    if (!API_CONFIG.DUNE.apiKey) {
      throw new Error('Dune API key not configured');
    }

    try {
      // Query for token holder distribution
      const query = `
        SELECT 
          holder_address,
          balance,
          (balance / total_supply * 100) as percentage
        FROM token_holders 
        WHERE token_address = '${tokenAddress}'
        ORDER BY balance DESC
        LIMIT 100
      `;

      const response = await fetch(`${API_CONFIG.DUNE.baseUrl}/query/execute`, {
        method: 'POST',
        headers: {
          'X-Dune-API-Key': API_CONFIG.DUNE.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query_sql: query,
          parameters: { token_address: tokenAddress },
        }),
      });

      if (!response.ok) {
        throw new Error(`Dune API error: ${response.status}`);
      }

      const data = await response.json();
      return {
        source: 'dune',
        holders: data.result?.rows || [],
        confidence: 0.9, // High confidence in Dune data
      };

    } catch (error) {
      console.error('[MultiSourceFetcher] Dune API error:', error);
      return null;
    }
  }

  /**
   * Fetch data from Alchemy (direct blockchain access)
   */
  private async fetchFromAlchemy(tokenAddress: string): Promise<any> {
    if (!API_CONFIG.ALCHEMY.baseUrl) {
      throw new Error('Alchemy URL not configured');
    }

    try {
      // Get token metadata
      const response = await fetch(API_CONFIG.ALCHEMY.baseUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'alchemy_getTokenMetadata',
          params: [tokenAddress],
          id: 1,
        }),
      });

      if (!response.ok) {
        throw new Error(`Alchemy API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(`Alchemy RPC error: ${data.error.message}`);
      }

      return {
        source: 'alchemy',
        metadata: data.result,
        confidence: 0.95, // Very high confidence in direct blockchain data
      };

    } catch (error) {
      console.error('[MultiSourceFetcher] Alchemy API error:', error);
      return null;
    }
  }

  /**
   * Fetch data from Birdeye (with better error handling)
   */
  private async fetchFromBirdeye(tokenAddress: string): Promise<any> {
    if (!API_CONFIG.BIRDEYE.apiKey) {
      return null;
    }

    try {
      const response = await fetch(`${API_CONFIG.BIRDEYE.baseUrl}/token_overview?address=${tokenAddress}`, {
        headers: { 'X-API-KEY': API_CONFIG.BIRDEYE.apiKey },
      });

      if (response.status === 429) {
        console.warn('[MultiSourceFetcher] Birdeye rate limited, skipping');
        return null;
      }

      if (!response.ok) {
        throw new Error(`Birdeye API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        return null;
      }

      return {
        source: 'birdeye',
        overview: data.data,
        confidence: 0.8,
      };

    } catch (error) {
      console.error('[MultiSourceFetcher] Birdeye API error:', error);
      return null;
    }
  }

  /**
   * Fetch data from DexScreener (reliable backup)
   */
  private async fetchFromDexScreener(tokenAddress: string): Promise<any> {
    try {
      const response = await fetch(`${API_CONFIG.DEXSCREENER.baseUrl}/tokens/${tokenAddress}`);

      if (!response.ok) {
        throw new Error(`DexScreener API error: ${response.status}`);
      }

      const data = await response.json();

      if (!data.pairs || data.pairs.length === 0) {
        return null;
      }

      const mainPair = data.pairs.reduce((prev: any, current: any) => 
        (current.liquidity?.usd || 0) > (prev.liquidity?.usd || 0) ? current : prev
      );

      return {
        source: 'dexscreener',
        pair: mainPair,
        confidence: 0.7,
      };

    } catch (error) {
      console.error('[MultiSourceFetcher] DexScreener API error:', error);
      return null;
    }
  }

  /**
   * Fetch contract data from Etherscan
   */
  private async fetchFromEtherscan(tokenAddress: string): Promise<any> {
    if (!API_CONFIG.ETHERSCAN.apiKey) {
      return null;
    }

    try {
      const response = await fetch(
        `${API_CONFIG.ETHERSCAN.baseUrl}?module=contract&action=getsourcecode&address=${tokenAddress}&apikey=${API_CONFIG.ETHERSCAN.apiKey}`
      );

      if (!response.ok) {
        throw new Error(`Etherscan API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== '1') {
        return null;
      }

      return {
        source: 'etherscan',
        contract: data.result[0],
        confidence: 0.9,
      };

    } catch (error) {
      console.error('[MultiSourceFetcher] Etherscan API error:', error);
      return null;
    }
  }

  /**
   * Aggregate data from multiple sources into unified format
   */
  private aggregateData(tokenAddress: string, sources: any): TokenData {
    const now = new Date().toISOString();
    let aggregated: Partial<TokenData> = {
      address: tokenAddress,
      lastUpdated: now,
    };

    let totalConfidence = 0;
    let sourceCount = 0;
    const dataSources: string[] = [];

    // Process each source
    Object.entries(sources).forEach(([sourceName, sourceData]) => {
      if (!sourceData) return;

      dataSources.push(sourceName);
      totalConfidence += sourceData.confidence;
      sourceCount++;

      // Aggregate based on source type
      switch (sourceName) {
        case 'dexscreener':
          if (sourceData.pair) {
            aggregated.name = sourceData.pair.baseToken?.name;
            aggregated.symbol = sourceData.pair.baseToken?.symbol;
            aggregated.price = parseFloat(sourceData.pair.priceUsd || '0');
            aggregated.marketCap = sourceData.pair.marketCap;
            aggregated.volume24h = sourceData.pair.volume?.h24;
            aggregated.liquidityUSD = sourceData.pair.liquidity?.usd;
          }
          break;

        case 'birdeye':
          if (sourceData.overview) {
            aggregated.name = sourceData.overview.name;
            aggregated.symbol = sourceData.overview.symbol;
            aggregated.price = sourceData.overview.price;
            aggregated.marketCap = sourceData.overview.mc;
            aggregated.volume24h = sourceData.overview.v24h;
            aggregated.holders = sourceData.overview.holders;
          }
          break;

        case 'dune':
          if (sourceData.holders?.length > 0) {
            aggregated.topHolders = sourceData.holders.slice(0, 20).map((holder: any) => ({
              address: holder.holder_address,
              balance: holder.balance,
              percentage: holder.percentage,
            }));
          }
          break;

        case 'alchemy':
          if (sourceData.metadata) {
            aggregated.name = sourceData.metadata.name;
            aggregated.symbol = sourceData.metadata.symbol;
            aggregated.totalSupply = sourceData.metadata.totalSupply;
          }
          break;

        case 'etherscan':
          if (sourceData.contract) {
            aggregated.contractVerified = !!sourceData.contract.SourceCode;
            aggregated.creatorAddress = sourceData.contract.ContractCreator;
          }
          break;
      }
    });

    // Calculate overall confidence
    const confidence = sourceCount > 0 ? totalConfidence / sourceCount : 0.1;

    return TokenDataSchema.parse({
      ...aggregated,
      dataSource: dataSources.join(', '),
      confidence: Math.min(confidence, 1),
    });
  }

  /**
   * Clear cache (useful for testing)
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const multiSourceFetcher = new MultiSourceFetcher();
