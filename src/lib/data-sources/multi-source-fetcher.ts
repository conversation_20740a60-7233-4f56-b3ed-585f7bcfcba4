/**
 * @fileOverview Multi-Source Data Fetcher - Uses all available APIs for maximum data coverage
 * Implements fallback strategies and data aggregation from multiple sources
 */

import { z } from 'zod';

// Comprehensive token data schema
export const TokenDataSchema = z.object({
  address: z.string(),
  name: z.string().optional(),
  symbol: z.string().optional(),
  
  // Market data
  price: z.number().optional(),
  marketCap: z.number().optional(),
  volume24h: z.number().optional(),
  
  // On-chain metrics
  totalSupply: z.number().optional(),
  circulatingSupply: z.number().optional(),
  holders: z.number().optional(),
  
  // Liquidity data
  liquidityUSD: z.number().optional(),
  liquidityChange24h: z.number().optional(),
  
  // Holder distribution
  topHolders: z.array(z.object({
    address: z.string(),
    balance: z.number(),
    percentage: z.number(),
  })).optional(),
  
  // Contract info
  contractVerified: z.boolean().optional(),
  creatorAddress: z.string().optional(),
  deploymentTime: z.string().optional(),
  
  // Metadata
  dataSource: z.string(),
  confidence: z.number().min(0).max(1),
  lastUpdated: z.string(),
});

export type TokenData = z.infer<typeof TokenDataSchema>;

// API configuration
const API_CONFIG = {
  DUNE: {
    baseUrl: 'https://api.dune.com/api/v1',
    apiKey: process.env.DUNE_API_KEY,
  },
  ALCHEMY: {
    baseUrl: process.env.WEB3_PROVIDER_URL,
  },
  ETHERSCAN: {
    baseUrl: 'https://api.etherscan.io/api',
    apiKey: process.env.ETHERSCAN_API_KEY,
  },
  BIRDEYE: {
    baseUrl: 'https://public-api.birdeye.so/defi',
    apiKey: process.env.API_BIRDEYE_API_KEY,
  },
  DEXSCREENER: {
    baseUrl: 'https://api.dexscreener.com/latest/dex',
  },
  SOLSCAN: {
    baseUrl: 'https://pro-api.solscan.io/v1.0',
    // Solscan doesn't require API key for basic endpoints
  },
  JUPITER: {
    baseUrl: 'https://price.jup.ag/v6',
    // Jupiter doesn't require API key
  },
  HELIUS: {
    baseUrl: 'https://mainnet.helius-rpc.com',
    apiKey: process.env.HELIUS_API_KEY,
  },
  COINGECKO: {
    baseUrl: 'https://api.coingecko.com/api/v3',
    // CoinGecko free tier doesn't require API key
  },
};

/**
 * Multi-source token data fetcher with intelligent fallbacks
 */
export class MultiSourceFetcher {
  private cache = new Map<string, { data: TokenData; expires: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Fetch comprehensive token data from multiple sources
   */
  async fetchTokenData(tokenAddress: string): Promise<TokenData> {
    // Check cache first
    const cached = this.cache.get(tokenAddress);
    if (cached && cached.expires > Date.now()) {
      return cached.data;
    }

    console.log(`[MultiSourceFetcher] Fetching data for ${tokenAddress}`);

    // Fetch from multiple sources in parallel
    const [
      duneData,
      alchemyData,
      birdeyeData,
      dexScreenerData,
      etherscanData,
      solscanData,
      jupiterData,
      heliusData,
      coingeckoData,
    ] = await Promise.allSettled([
      this.fetchFromDune(tokenAddress),
      this.fetchFromAlchemy(tokenAddress),
      this.fetchFromBirdeye(tokenAddress),
      this.fetchFromDexScreener(tokenAddress),
      this.fetchFromEtherscan(tokenAddress),
      this.fetchFromSolscan(tokenAddress),
      this.fetchFromJupiter(tokenAddress),
      this.fetchFromHelius(tokenAddress),
      this.fetchFromCoinGecko(tokenAddress),
    ]);

    // Aggregate data from successful sources
    const aggregatedData = this.aggregateData(tokenAddress, {
      dune: duneData.status === 'fulfilled' ? duneData.value : null,
      alchemy: alchemyData.status === 'fulfilled' ? alchemyData.value : null,
      birdeye: birdeyeData.status === 'fulfilled' ? birdeyeData.value : null,
      dexscreener: dexScreenerData.status === 'fulfilled' ? dexScreenerData.value : null,
      etherscan: etherscanData.status === 'fulfilled' ? etherscanData.value : null,
      solscan: solscanData.status === 'fulfilled' ? solscanData.value : null,
      jupiter: jupiterData.status === 'fulfilled' ? jupiterData.value : null,
      helius: heliusData.status === 'fulfilled' ? heliusData.value : null,
      coingecko: coingeckoData.status === 'fulfilled' ? coingeckoData.value : null,
    });

    // Cache the result
    this.cache.set(tokenAddress, {
      data: aggregatedData,
      expires: Date.now() + this.CACHE_TTL,
    });

    return aggregatedData;
  }

  /**
   * Fetch data from Dune Analytics (professional blockchain analytics)
   */
  private async fetchFromDune(tokenAddress: string): Promise<any> {
    if (!API_CONFIG.DUNE.apiKey) {
      throw new Error('Dune API key not configured');
    }

    try {
      // Query for token holder distribution
      const query = `
        SELECT 
          holder_address,
          balance,
          (balance / total_supply * 100) as percentage
        FROM token_holders 
        WHERE token_address = '${tokenAddress}'
        ORDER BY balance DESC
        LIMIT 100
      `;

      const response = await fetch(`${API_CONFIG.DUNE.baseUrl}/query/execute`, {
        method: 'POST',
        headers: {
          'X-Dune-API-Key': API_CONFIG.DUNE.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query_sql: query,
          parameters: { token_address: tokenAddress },
        }),
      });

      if (!response.ok) {
        // Don't throw error, just log and return null for graceful fallback
        console.log(`[MultiSourceFetcher] Dune API ${response.status}, falling back to other sources`);
        return null;
      }

      const data = await response.json();
      return {
        source: 'dune',
        holders: data.result?.rows || [],
        confidence: 0.95, // Very high confidence in Dune data
      };

    } catch (error) {
      console.log('[MultiSourceFetcher] Dune API error, using fallbacks:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Fetch data from Alchemy (direct blockchain access)
   * Note: Alchemy's Solana support is limited, using for basic account info
   */
  private async fetchFromAlchemy(tokenAddress: string): Promise<any> {
    if (!API_CONFIG.ALCHEMY.baseUrl) {
      console.log('[MultiSourceFetcher] Alchemy URL not configured, skipping');
      return null;
    }

    try {
      // Use Solana RPC method for account info
      const response = await fetch(API_CONFIG.ALCHEMY.baseUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'getAccountInfo',
          params: [
            tokenAddress,
            {
              encoding: 'base64',
            }
          ],
          id: 1,
        }),
      });

      if (!response.ok) {
        console.log(`[MultiSourceFetcher] Alchemy API ${response.status}, falling back to other sources`);
        return null;
      }

      const data = await response.json();

      if (data.error) {
        console.log(`[MultiSourceFetcher] Alchemy RPC error: ${data.error.message}`);
        return null;
      }

      // Basic account info - limited usefulness for token analysis
      return {
        source: 'alchemy',
        accountInfo: data.result,
        confidence: 0.6, // Lower confidence as this is just basic account data
      };

    } catch (error) {
      console.log('[MultiSourceFetcher] Alchemy API error, using fallbacks:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Fetch data from Birdeye (with better error handling)
   */
  private async fetchFromBirdeye(tokenAddress: string): Promise<any> {
    if (!API_CONFIG.BIRDEYE.apiKey) {
      return null;
    }

    try {
      const response = await fetch(`${API_CONFIG.BIRDEYE.baseUrl}/token_overview?address=${tokenAddress}`, {
        headers: { 'X-API-KEY': API_CONFIG.BIRDEYE.apiKey },
      });

      if (response.status === 429) {
        console.warn('[MultiSourceFetcher] Birdeye rate limited, skipping');
        return null;
      }

      if (!response.ok) {
        throw new Error(`Birdeye API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        return null;
      }

      return {
        source: 'birdeye',
        overview: data.data,
        confidence: 0.8,
      };

    } catch (error) {
      console.error('[MultiSourceFetcher] Birdeye API error:', error);
      return null;
    }
  }

  /**
   * Fetch data from DexScreener (reliable backup)
   */
  private async fetchFromDexScreener(tokenAddress: string): Promise<any> {
    try {
      const response = await fetch(`${API_CONFIG.DEXSCREENER.baseUrl}/tokens/${tokenAddress}`);

      if (!response.ok) {
        throw new Error(`DexScreener API error: ${response.status}`);
      }

      const data = await response.json();

      if (!data.pairs || data.pairs.length === 0) {
        return null;
      }

      const mainPair = data.pairs.reduce((prev: any, current: any) => 
        (current.liquidity?.usd || 0) > (prev.liquidity?.usd || 0) ? current : prev
      );

      return {
        source: 'dexscreener',
        pair: mainPair,
        confidence: 0.7,
      };

    } catch (error) {
      console.error('[MultiSourceFetcher] DexScreener API error:', error);
      return null;
    }
  }

  /**
   * Fetch contract data from Etherscan
   */
  private async fetchFromEtherscan(tokenAddress: string): Promise<any> {
    if (!API_CONFIG.ETHERSCAN.apiKey) {
      return null;
    }

    try {
      const response = await fetch(
        `${API_CONFIG.ETHERSCAN.baseUrl}?module=contract&action=getsourcecode&address=${tokenAddress}&apikey=${API_CONFIG.ETHERSCAN.apiKey}`
      );

      if (!response.ok) {
        throw new Error(`Etherscan API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== '1') {
        return null;
      }

      return {
        source: 'etherscan',
        contract: data.result[0],
        confidence: 0.9,
      };

    } catch (error) {
      console.error('[MultiSourceFetcher] Etherscan API error:', error);
      return null;
    }
  }

  /**
   * Fetch data from Solscan (Solana blockchain explorer)
   */
  private async fetchFromSolscan(tokenAddress: string): Promise<any> {
    try {
      // Get token metadata and holder count
      const [metadataResponse, holdersResponse] = await Promise.allSettled([
        fetch(`${API_CONFIG.SOLSCAN.baseUrl}/token/meta?tokenAddress=${tokenAddress}`),
        fetch(`${API_CONFIG.SOLSCAN.baseUrl}/token/holders?tokenAddress=${tokenAddress}&offset=0&limit=50`)
      ]);

      let metadata = null;
      let holders = null;

      if (metadataResponse.status === 'fulfilled' && metadataResponse.value.ok) {
        metadata = await metadataResponse.value.json();
      }

      if (holdersResponse.status === 'fulfilled' && holdersResponse.value.ok) {
        holders = await holdersResponse.value.json();
      }

      if (!metadata && !holders) {
        return null;
      }

      return {
        source: 'solscan',
        metadata: metadata?.data,
        holders: holders?.data,
        confidence: 0.85, // High confidence in Solscan data
      };

    } catch (error) {
      console.error('[MultiSourceFetcher] Solscan API error:', error);
      return null;
    }
  }

  /**
   * Aggregate data from multiple sources into unified format
   */
  private aggregateData(tokenAddress: string, sources: Record<string, any>): TokenData {
    const now = new Date().toISOString();
    let aggregated: Partial<TokenData> = {
      address: tokenAddress,
      lastUpdated: now,
    };

    let totalConfidence = 0;
    let sourceCount = 0;
    const dataSources: string[] = [];

    // Process each source
    Object.entries(sources).forEach(([sourceName, sourceData]) => {
      if (!sourceData || typeof sourceData !== 'object') return;

      dataSources.push(sourceName);
      totalConfidence += sourceData.confidence || 0.5;
      sourceCount++;

      // Aggregate based on source type
      switch (sourceName) {
        case 'dexscreener':
          if (sourceData.pair) {
            aggregated.name = sourceData.pair.baseToken?.name;
            aggregated.symbol = sourceData.pair.baseToken?.symbol;
            aggregated.price = parseFloat(sourceData.pair.priceUsd || '0');
            aggregated.marketCap = sourceData.pair.marketCap;
            aggregated.volume24h = sourceData.pair.volume?.h24;
            aggregated.liquidityUSD = sourceData.pair.liquidity?.usd;
          }
          break;

        case 'birdeye':
          if (sourceData.overview) {
            aggregated.name = sourceData.overview.name;
            aggregated.symbol = sourceData.overview.symbol;
            aggregated.price = sourceData.overview.price;
            aggregated.marketCap = sourceData.overview.mc;
            aggregated.volume24h = sourceData.overview.v24h;
            aggregated.holders = sourceData.overview.holders;
          }
          break;

        case 'dune':
          if (sourceData.holders?.length > 0) {
            aggregated.topHolders = sourceData.holders.slice(0, 20).map((holder: any) => ({
              address: holder.holder_address,
              balance: holder.balance,
              percentage: holder.percentage,
            }));
          }
          break;

        case 'alchemy':
          if (sourceData.metadata) {
            aggregated.name = sourceData.metadata.name;
            aggregated.symbol = sourceData.metadata.symbol;
            aggregated.totalSupply = sourceData.metadata.totalSupply;
          }
          break;

        case 'etherscan':
          if (sourceData.contract) {
            aggregated.contractVerified = !!sourceData.contract.SourceCode;
            aggregated.creatorAddress = sourceData.contract.ContractCreator;
          }
          break;

        case 'solscan':
          if (sourceData.metadata) {
            aggregated.name = sourceData.metadata.name;
            aggregated.symbol = sourceData.metadata.symbol;
            aggregated.totalSupply = sourceData.metadata.supply;
          }
          if (sourceData.holders) {
            aggregated.holders = sourceData.holders.total;
            aggregated.topHolders = sourceData.holders.data?.slice(0, 20).map((holder: any) => ({
              address: holder.address,
              balance: holder.amount,
              percentage: (holder.amount / (sourceData.metadata?.supply || 1)) * 100,
            })) || [];
          }
          break;

        case 'jupiter':
          if (sourceData.price) {
            aggregated.price = sourceData.price;
          }
          break;

        case 'helius':
          if (sourceData.supply) {
            aggregated.totalSupply = sourceData.supply.amount;
            aggregated.circulatingSupply = sourceData.supply.amount;
          }
          break;
      }
    });

    // Calculate overall confidence based on data completeness, not API failures
    let dataCompleteness = 0;
    let maxPossibleScore = 0;

    // Core market data (most important)
    maxPossibleScore += 0.4;
    if (aggregated.price && aggregated.marketCap && aggregated.volume24h) {
      dataCompleteness += 0.4; // Full market data
    } else if (aggregated.price || aggregated.marketCap) {
      dataCompleteness += 0.2; // Partial market data
    }

    // Liquidity data (very important for rug detection)
    maxPossibleScore += 0.3;
    if (aggregated.liquidityUSD && aggregated.liquidityUSD > 0) {
      dataCompleteness += 0.3;
    }

    // Holder data (important but often unavailable)
    maxPossibleScore += 0.2;
    if (aggregated.holders && aggregated.holders > 0) {
      dataCompleteness += 0.1;
    }
    if (aggregated.topHolders && aggregated.topHolders.length > 0) {
      dataCompleteness += 0.1;
    }

    // Contract verification (nice to have)
    maxPossibleScore += 0.1;
    if (aggregated.contractVerified !== undefined) {
      dataCompleteness += 0.1;
    }

    // Calculate confidence as percentage of available data we successfully retrieved
    const confidence = Math.min(dataCompleteness / Math.max(maxPossibleScore, 0.1), 1.0);

    // Minimum confidence boost if we have any real data
    const finalConfidence = Math.max(confidence, sourceCount > 0 ? 0.5 : 0.1);

    return TokenDataSchema.parse({
      ...aggregated,
      dataSource: dataSources.join(', '),
      confidence: finalConfidence,
    });
  }

  /**
   * Fetch data from Jupiter (Solana DEX aggregator)
   */
  private async fetchFromJupiter(tokenAddress: string): Promise<any> {
    try {
      // Get price data from Jupiter
      const priceResponse = await fetch(`${API_CONFIG.JUPITER.baseUrl}/price?ids=${tokenAddress}`);

      if (!priceResponse.ok) {
        console.log(`[MultiSourceFetcher] Jupiter price API ${priceResponse.status}, skipping`);
        return null;
      }

      const priceData = await priceResponse.json();

      if (!priceData.data || !priceData.data[tokenAddress]) {
        return null;
      }

      const tokenPrice = priceData.data[tokenAddress];

      return {
        source: 'jupiter',
        price: tokenPrice.price,
        confidence: 0.85, // High confidence in Jupiter price data
      };

    } catch (error) {
      console.log('[MultiSourceFetcher] Jupiter API error:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Fetch data from Helius (Enhanced Solana RPC)
   */
  private async fetchFromHelius(tokenAddress: string): Promise<any> {
    if (!API_CONFIG.HELIUS.apiKey) {
      console.log('[MultiSourceFetcher] Helius API key not configured, skipping');
      return null;
    }

    try {
      // Get token account info using Helius enhanced RPC
      const response = await fetch(`${API_CONFIG.HELIUS.baseUrl}/?api-key=${API_CONFIG.HELIUS.apiKey}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'getTokenSupply',
          params: [tokenAddress],
          id: 1,
        }),
      });

      if (!response.ok) {
        console.log(`[MultiSourceFetcher] Helius API ${response.status}, skipping`);
        return null;
      }

      const data = await response.json();

      if (data.error) {
        console.log(`[MultiSourceFetcher] Helius RPC error: ${data.error.message}`);
        return null;
      }

      return {
        source: 'helius',
        supply: data.result?.value,
        confidence: 0.9, // Very high confidence in Helius data
      };

    } catch (error) {
      console.log('[MultiSourceFetcher] Helius API error:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Fetch data from CoinGecko (comprehensive crypto data)
   */
  private async fetchFromCoinGecko(tokenAddress: string): Promise<any> {
    try {
      // CoinGecko uses contract addresses for tokens
      const response = await fetch(`${API_CONFIG.COINGECKO.baseUrl}/coins/solana/contract/${tokenAddress}`);

      if (!response.ok) {
        console.log(`[MultiSourceFetcher] CoinGecko API ${response.status}, skipping`);
        return null;
      }

      const data = await response.json();

      if (!data.id) {
        return null;
      }

      return {
        source: 'coingecko',
        name: data.name,
        symbol: data.symbol,
        price: data.market_data?.current_price?.usd,
        marketCap: data.market_data?.market_cap?.usd,
        volume24h: data.market_data?.total_volume?.usd,
        confidence: 0.9, // Very high confidence in CoinGecko data
      };

    } catch (error) {
      console.log('[MultiSourceFetcher] CoinGecko API error:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Clear cache (useful for testing)
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const multiSourceFetcher = new MultiSourceFetcher();
