'use server';

import {
  generateTokenSentinelReport,
  type TokenSentinelFlowOutput,
} from '@/ai/flows/token-sentinel-flow';
import {z} from 'zod';

const TokenReportSchema = z.object({
  tokenAddress: z.string().min(1, 'Token address is required.'),
  tokenName: z.string().optional(),
  tokenTicker: z.string().optional(),
});

export type FormState = {
  status: 'success' | 'error' | 'idle' | 'generating';
  message: string;
  data: TokenSentinelFlowOutput | null;
};

export async function getTokenReportAction(
  prevState: FormState,
  formData: FormData
): Promise<FormState> {
  const validatedFields = TokenReportSchema.safeParse({
    tokenAddress: formData.get('tokenAddress'),
    tokenName: formData.get('tokenName'),
    tokenTicker: formData.get('tokenTicker'),
  });

  if (!validatedFields.success) {
    return {
      status: 'error',
      message: 'Invalid form data. Token address is required.',
      data: null,
    };
  }

  try {
    const input = validatedFields.data;
    // Set the state to generating before calling the AI
    // We can't stream with useActionState, so this provides immediate feedback.
    // The UI will handle this state.

    const report = await generateTokenSentinelReport(input);

    // This is a critical validation step. If the AI couldn't find the token's name,
    // it means the primary tool failed, and the report is likely generic and useless.
    if (!report || !report.tokenName || report.tokenName === 'Unknown Name') {
      return {
        status: 'error',
        message:
          report?.keyInformation ||
          'Failed to retrieve token data. The address may be invalid, or the token is too new to be indexed. Please try again in a few moments.',
        data: null,
      };
    }

    return {
      status: 'success',
      message: 'Report generated successfully.',
      data: report,
    };
  } catch (error) {
    console.error(error);
    const errorMessage =
      error instanceof Error ? error.message : 'An unknown error occurred.';
    return {
      status: 'error',
      message: `Failed to generate report: ${errorMessage}`,
      data: null,
    };
  }
}
