/**
 * @fileOverview TokenSentinel Validation & Testing System
 * Provides comprehensive testing, backtesting, and validation capabilities
 */

import { z } from 'zod';
import { executeTokenSentinel, type TokenSentinelInput, type TokenSentinelOutput } from '../ai/workflows/token-sentinel';

// Test case schema
export const TestCase = z.object({
  tokenAddress: z.string(),
  tokenName: z.string(),
  expectedRiskLevel: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  expectedRugPull: z.boolean(),
  actualOutcome: z.enum(['SAFE', 'RUG_PULL', 'UNKNOWN']).optional(),
  notes: z.string().optional(),
  timestamp: z.string(),
});

export type TestCase = z.infer<typeof TestCase>;

// Validation result schema
export const ValidationResult = z.object({
  testCase: TestCase,
  prediction: TokenSentinelOutput,
  accuracy: z.object({
    riskLevelCorrect: z.boolean(),
    rugPullCorrect: z.boolean(),
    overallScore: z.number().min(0).max(1),
  }),
  executionTime: z.number(),
  errors: z.array(z.string()),
});

export type ValidationResult = z.infer<typeof ValidationResult>;

// Known rug pull test cases (historical data)
const KNOWN_RUG_PULLS: TestCase[] = [
  {
    tokenAddress: '7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr', // Example Solana rug pull
    tokenName: 'Squid Game Token',
    expectedRiskLevel: 'CRITICAL',
    expectedRugPull: true,
    actualOutcome: 'RUG_PULL',
    notes: 'Classic rug pull with hidden mint function',
    timestamp: '2024-01-15T10:00:00Z',
  },
  // Add more historical cases...
];

// Known safe tokens for testing
const KNOWN_SAFE_TOKENS: TestCase[] = [
  {
    tokenAddress: 'So11111111111111111111111111111111111111112', // Wrapped SOL
    tokenName: 'Wrapped SOL',
    expectedRiskLevel: 'LOW',
    expectedRugPull: false,
    actualOutcome: 'SAFE',
    notes: 'Well-established wrapped token',
    timestamp: '2024-01-15T10:00:00Z',
  },
  // Add more safe tokens...
];

/**
 * Run comprehensive validation test suite
 */
export async function runValidationSuite(): Promise<{
  results: ValidationResult[];
  summary: {
    totalTests: number;
    passed: number;
    failed: number;
    accuracy: number;
    averageExecutionTime: number;
  };
}> {
  console.log('[TokenSentinelValidator] Starting validation suite...');
  
  const allTestCases = [...KNOWN_RUG_PULLS, ...KNOWN_SAFE_TOKENS];
  const results: ValidationResult[] = [];
  
  for (const testCase of allTestCases) {
    try {
      const startTime = Date.now();
      
      const input: TokenSentinelInput = {
        tokenAddress: testCase.tokenAddress,
        tokenName: testCase.tokenName,
        priority: 'HIGH',
        skipSocialAnalysis: false,
      };
      
      const prediction = await executeTokenSentinel(input);
      const executionTime = Date.now() - startTime;
      
      // Calculate accuracy
      const accuracy = calculateAccuracy(testCase, prediction);
      
      const result: ValidationResult = {
        testCase,
        prediction,
        accuracy,
        executionTime,
        errors: prediction.errors,
      };
      
      results.push(result);
      
      console.log(`[TokenSentinelValidator] Test ${testCase.tokenName}: ${accuracy.overallScore.toFixed(2)} accuracy`);
      
    } catch (error) {
      console.error(`[TokenSentinelValidator] Test failed for ${testCase.tokenName}:`, error);
      
      results.push({
        testCase,
        prediction: {} as TokenSentinelOutput, // Empty prediction for failed test
        accuracy: { riskLevelCorrect: false, rugPullCorrect: false, overallScore: 0 },
        executionTime: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      });
    }
    
    // Rate limiting between tests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Calculate summary statistics
  const summary = calculateSummary(results);
  
  console.log(`[TokenSentinelValidator] Validation complete: ${summary.accuracy.toFixed(2)} accuracy`);
  
  return { results, summary };
}

/**
 * Calculate accuracy metrics for a test case
 */
function calculateAccuracy(testCase: TestCase, prediction: TokenSentinelOutput): {
  riskLevelCorrect: boolean;
  rugPullCorrect: boolean;
  overallScore: number;
} {
  // Check risk level accuracy
  const riskLevelCorrect = testCase.expectedRiskLevel === prediction.alertLevel;
  
  // Check rug pull prediction accuracy
  const predictedRugPull = prediction.rugPullProbability > 0.7;
  const rugPullCorrect = testCase.expectedRugPull === predictedRugPull;
  
  // Calculate overall score (weighted)
  let overallScore = 0;
  if (riskLevelCorrect) overallScore += 0.6; // Risk level is 60% of score
  if (rugPullCorrect) overallScore += 0.4;   // Rug pull prediction is 40% of score
  
  return {
    riskLevelCorrect,
    rugPullCorrect,
    overallScore,
  };
}

/**
 * Calculate summary statistics
 */
function calculateSummary(results: ValidationResult[]): {
  totalTests: number;
  passed: number;
  failed: number;
  accuracy: number;
  averageExecutionTime: number;
} {
  const totalTests = results.length;
  const passed = results.filter(r => r.accuracy.overallScore >= 0.7).length;
  const failed = totalTests - passed;
  
  const totalAccuracy = results.reduce((sum, r) => sum + r.accuracy.overallScore, 0);
  const accuracy = totalTests > 0 ? totalAccuracy / totalTests : 0;
  
  const totalExecutionTime = results.reduce((sum, r) => sum + r.executionTime, 0);
  const averageExecutionTime = totalTests > 0 ? totalExecutionTime / totalTests : 0;
  
  return {
    totalTests,
    passed,
    failed,
    accuracy,
    averageExecutionTime,
  };
}

/**
 * Backtest with historical data
 */
export async function backtestHistoricalData(
  historicalTokens: Array<{
    address: string;
    name: string;
    launchDate: string;
    rugPullDate?: string;
    finalOutcome: 'SAFE' | 'RUG_PULL' | 'FAILED';
  }>
): Promise<{
  predictions: Array<{
    token: any;
    prediction: TokenSentinelOutput;
    correct: boolean;
  }>;
  accuracy: number;
}> {
  console.log(`[TokenSentinelValidator] Backtesting ${historicalTokens.length} historical tokens...`);
  
  const predictions = [];
  
  for (const token of historicalTokens) {
    try {
      const input: TokenSentinelInput = {
        tokenAddress: token.address,
        tokenName: token.name,
        priority: 'MEDIUM',
        skipSocialAnalysis: true, // Skip for historical data
      };
      
      const prediction = await executeTokenSentinel(input);
      
      // Determine if prediction was correct
      const predictedRugPull = prediction.rugPullProbability > 0.7;
      const actualRugPull = token.finalOutcome === 'RUG_PULL';
      const correct = predictedRugPull === actualRugPull;
      
      predictions.push({
        token,
        prediction,
        correct,
      });
      
      console.log(`[TokenSentinelValidator] ${token.name}: ${correct ? 'CORRECT' : 'INCORRECT'}`);
      
    } catch (error) {
      console.error(`[TokenSentinelValidator] Backtest failed for ${token.name}:`, error);
      
      predictions.push({
        token,
        prediction: {} as TokenSentinelOutput,
        correct: false,
      });
    }
    
    // Rate limiting
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  const correctPredictions = predictions.filter(p => p.correct).length;
  const accuracy = predictions.length > 0 ? correctPredictions / predictions.length : 0;
  
  console.log(`[TokenSentinelValidator] Backtest accuracy: ${(accuracy * 100).toFixed(1)}%`);
  
  return { predictions, accuracy };
}

/**
 * Performance stress test
 */
export async function performanceStressTest(
  tokenAddresses: string[],
  concurrency: number = 3
): Promise<{
  totalTokens: number;
  successfulAnalyses: number;
  failedAnalyses: number;
  averageExecutionTime: number;
  maxExecutionTime: number;
  minExecutionTime: number;
  throughputPerMinute: number;
}> {
  console.log(`[TokenSentinelValidator] Starting stress test with ${tokenAddresses.length} tokens...`);
  
  const startTime = Date.now();
  const results: { success: boolean; executionTime: number }[] = [];
  
  // Process tokens in batches with controlled concurrency
  for (let i = 0; i < tokenAddresses.length; i += concurrency) {
    const batch = tokenAddresses.slice(i, i + concurrency);
    
    const batchPromises = batch.map(async (address) => {
      const tokenStartTime = Date.now();
      
      try {
        await executeTokenSentinel({
          tokenAddress: address,
          priority: 'LOW',
          skipSocialAnalysis: true,
        });
        
        return {
          success: true,
          executionTime: Date.now() - tokenStartTime,
        };
        
      } catch (error) {
        return {
          success: false,
          executionTime: Date.now() - tokenStartTime,
        };
      }
    });
    
    const batchResults = await Promise.allSettled(batchPromises);
    
    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        results.push({ success: false, executionTime: 0 });
      }
    }
    
    // Small delay between batches
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  const totalTime = Date.now() - startTime;
  const successfulAnalyses = results.filter(r => r.success).length;
  const failedAnalyses = results.length - successfulAnalyses;
  
  const executionTimes = results.filter(r => r.success).map(r => r.executionTime);
  const averageExecutionTime = executionTimes.length > 0 ? 
    executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length : 0;
  
  const maxExecutionTime = executionTimes.length > 0 ? Math.max(...executionTimes) : 0;
  const minExecutionTime = executionTimes.length > 0 ? Math.min(...executionTimes) : 0;
  
  const throughputPerMinute = (successfulAnalyses / totalTime) * 60000; // Convert to per minute
  
  console.log(`[TokenSentinelValidator] Stress test complete: ${successfulAnalyses}/${results.length} successful`);
  
  return {
    totalTokens: results.length,
    successfulAnalyses,
    failedAnalyses,
    averageExecutionTime,
    maxExecutionTime,
    minExecutionTime,
    throughputPerMinute,
  };
}

/**
 * Generate validation report
 */
export function generateValidationReport(
  validationResults: ValidationResult[],
  backtestResults?: any,
  stressTestResults?: any
): string {
  let report = '# TokenSentinel Validation Report\n\n';
  
  // Validation suite results
  report += '## Validation Suite Results\n\n';
  const summary = calculateSummary(validationResults);
  report += `- **Total Tests:** ${summary.totalTests}\n`;
  report += `- **Passed:** ${summary.passed}\n`;
  report += `- **Failed:** ${summary.failed}\n`;
  report += `- **Overall Accuracy:** ${(summary.accuracy * 100).toFixed(1)}%\n`;
  report += `- **Average Execution Time:** ${summary.averageExecutionTime.toFixed(0)}ms\n\n`;
  
  // Detailed results
  report += '### Detailed Results\n\n';
  for (const result of validationResults) {
    report += `**${result.testCase.tokenName}**\n`;
    report += `- Expected: ${result.testCase.expectedRiskLevel} risk\n`;
    report += `- Predicted: ${result.prediction.alertLevel} risk\n`;
    report += `- Accuracy: ${(result.accuracy.overallScore * 100).toFixed(1)}%\n`;
    report += `- Execution Time: ${result.executionTime}ms\n\n`;
  }
  
  // Backtest results
  if (backtestResults) {
    report += '## Backtest Results\n\n';
    report += `- **Historical Accuracy:** ${(backtestResults.accuracy * 100).toFixed(1)}%\n`;
    report += `- **Tokens Tested:** ${backtestResults.predictions.length}\n\n`;
  }
  
  // Stress test results
  if (stressTestResults) {
    report += '## Performance Stress Test\n\n';
    report += `- **Success Rate:** ${((stressTestResults.successfulAnalyses / stressTestResults.totalTokens) * 100).toFixed(1)}%\n`;
    report += `- **Average Execution Time:** ${stressTestResults.averageExecutionTime.toFixed(0)}ms\n`;
    report += `- **Throughput:** ${stressTestResults.throughputPerMinute.toFixed(1)} tokens/minute\n\n`;
  }
  
  report += '---\n';
  report += `Report generated on ${new Date().toISOString()}\n`;
  
  return report;
}
