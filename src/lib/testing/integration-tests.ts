'use server';

/**
 * @fileOverview TokenSentinel Integration Tests
 * Comprehensive test suite for the entire TokenSentinel system
 */

import { executeTokenSentinel } from '../../ai/workflows/token-sentinel';
import { generateTokenSentinelReport } from '../../ai/flows/token-sentinel-flow';
import { sendTokenAlert } from '../telegram-alerts';
import { runValidationSuite } from './token-sentinel-validator';

// Test configuration
const TEST_CONFIG = {
  TIMEOUT_MS: 30000,
  RETRY_ATTEMPTS: 3,
  DELAY_BETWEEN_TESTS: 2000,
};

// Test tokens (use real addresses for integration testing)
const TEST_TOKENS = {
  SAFE_TOKEN: 'So11111111111111111111111111111111111111112', // Wrapped SOL
  RISKY_TOKEN: '7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr', // Example risky token
  INVALID_TOKEN: 'invalid_address_for_testing',
};

interface TestResult {
  testName: string;
  passed: boolean;
  duration: number;
  error?: string;
  details?: any;
}

/**
 * Run complete integration test suite
 */
export async function runIntegrationTests(): Promise<{
  results: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    duration: number;
  };
}> {
  console.log('[IntegrationTests] Starting TokenSentinel integration tests...');
  
  const startTime = Date.now();
  const results: TestResult[] = [];
  
  // Test 1: Basic TokenSentinel workflow
  results.push(await runTest('Basic TokenSentinel Workflow', testBasicWorkflow));
  
  // Test 2: UI Integration
  results.push(await runTest('UI Integration Flow', testUIIntegration));
  
  // Test 3: Alert System
  results.push(await runTest('Telegram Alert System', testAlertSystem));
  
  // Test 4: Error Handling
  results.push(await runTest('Error Handling', testErrorHandling));
  
  // Test 5: Performance Test
  results.push(await runTest('Performance Test', testPerformance));
  
  // Test 6: Agent Communication
  results.push(await runTest('Multi-Agent Communication', testAgentCommunication));
  
  // Test 7: API Integration
  results.push(await runTest('External API Integration', testAPIIntegration));
  
  // Test 8: Risk Calculation
  results.push(await runTest('Risk Calculation Accuracy', testRiskCalculation));
  
  const totalDuration = Date.now() - startTime;
  const passed = results.filter(r => r.passed).length;
  const failed = results.length - passed;
  
  console.log(`[IntegrationTests] Tests completed: ${passed}/${results.length} passed`);
  
  return {
    results,
    summary: {
      total: results.length,
      passed,
      failed,
      duration: totalDuration,
    },
  };
}

/**
 * Test runner helper
 */
async function runTest(
  testName: string,
  testFunction: () => Promise<any>
): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    console.log(`[IntegrationTests] Running: ${testName}`);
    
    const details = await Promise.race([
      testFunction(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Test timeout')), TEST_CONFIG.TIMEOUT_MS)
      ),
    ]);
    
    const duration = Date.now() - startTime;
    
    console.log(`[IntegrationTests] ✅ ${testName} passed (${duration}ms)`);
    
    return {
      testName,
      passed: true,
      duration,
      details,
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    console.error(`[IntegrationTests] ❌ ${testName} failed: ${errorMessage}`);
    
    return {
      testName,
      passed: false,
      duration,
      error: errorMessage,
    };
  } finally {
    // Delay between tests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.DELAY_BETWEEN_TESTS));
  }
}

/**
 * Test 1: Basic TokenSentinel workflow
 */
async function testBasicWorkflow(): Promise<any> {
  const result = await executeTokenSentinel({
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    tokenName: 'Wrapped SOL',
    tokenSymbol: 'WSOL',
    priority: 'HIGH',
    skipSocialAnalysis: true, // Skip to avoid rate limits in testing
  });
  
  // Validate result structure
  if (!result.tokenAddress || !result.overallRiskScore || !result.alertLevel) {
    throw new Error('Invalid result structure');
  }
  
  // Validate risk score range
  if (result.overallRiskScore < 0 || result.overallRiskScore > 10) {
    throw new Error('Risk score out of range');
  }
  
  // Validate confidence range
  if (result.confidence < 0 || result.confidence > 1) {
    throw new Error('Confidence out of range');
  }
  
  return {
    riskScore: result.overallRiskScore,
    alertLevel: result.alertLevel,
    executionTime: result.executionTime,
    agentsExecuted: result.agentsExecuted.length,
  };
}

/**
 * Test 2: UI Integration flow
 */
async function testUIIntegration(): Promise<any> {
  const result = await generateTokenSentinelReport({
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    tokenName: 'Wrapped SOL',
    tokenTicker: 'WSOL',
  });
  
  // Validate UI-compatible output
  const requiredFields = [
    'keyInformation',
    'onChainAnalysis',
    'riskAssessment',
    'scenarioAnalysis',
    'overallAssessment',
    'tokenName',
    'tokenSymbol',
  ];
  
  for (const field of requiredFields) {
    if (!(field in result)) {
      throw new Error(`Missing required field: ${field}`);
    }
  }
  
  // Validate scenario analysis structure
  if (!result.scenarioAnalysis.bullish || !result.scenarioAnalysis.bearish) {
    throw new Error('Invalid scenario analysis structure');
  }
  
  return {
    fieldsPresent: requiredFields.length,
    riskScore: result.riskScore,
    confidence: result.confidence,
  };
}

/**
 * Test 3: Alert system
 */
async function testAlertSystem(): Promise<any> {
  // Create a mock high-risk result
  const mockResult = {
    tokenAddress: TEST_TOKENS.RISKY_TOKEN,
    tokenName: 'Test Risky Token',
    tokenSymbol: 'RISK',
    overallRiskScore: 8.5,
    rugPullProbability: 0.85,
    alertLevel: 'HIGH' as const,
    confidence: 0.9,
    priceDirection: 'DOWN' as const,
    executionTime: 5000,
    agentsExecuted: ['token_hunter', 'contract_auditor'],
    errors: [],
    shouldAlert: true,
    alertMessage: 'Test alert message',
    recommendedAction: 'AVOID',
  };
  
  // Test alert sending (will fail if Telegram not configured, but that's expected)
  try {
    const alertSent = await sendTokenAlert(mockResult);
    return { alertSent, telegramConfigured: true };
  } catch (error) {
    // Expected if Telegram not configured in test environment
    return { alertSent: false, telegramConfigured: false };
  }
}

/**
 * Test 4: Error handling
 */
async function testErrorHandling(): Promise<any> {
  // Test with invalid token address
  const result = await executeTokenSentinel({
    tokenAddress: TEST_TOKENS.INVALID_TOKEN,
    priority: 'LOW',
    skipSocialAnalysis: true,
  });
  
  // Should handle error gracefully
  if (result.errors.length === 0) {
    throw new Error('Expected errors for invalid token address');
  }
  
  // Should still return valid structure
  if (!result.alertLevel || !result.overallRiskScore) {
    throw new Error('Error handling did not return valid structure');
  }
  
  return {
    errorsHandled: result.errors.length,
    gracefulDegradation: true,
  };
}

/**
 * Test 5: Performance test
 */
async function testPerformance(): Promise<any> {
  const iterations = 3;
  const executionTimes: number[] = [];
  
  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now();
    
    await executeTokenSentinel({
      tokenAddress: TEST_TOKENS.SAFE_TOKEN,
      priority: 'LOW',
      skipSocialAnalysis: true,
    });
    
    executionTimes.push(Date.now() - startTime);
    
    // Small delay between iterations
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  const averageTime = executionTimes.reduce((sum, time) => sum + time, 0) / iterations;
  const maxTime = Math.max(...executionTimes);
  const minTime = Math.min(...executionTimes);
  
  // Performance thresholds
  if (averageTime > 30000) { // 30 seconds
    throw new Error(`Average execution time too high: ${averageTime}ms`);
  }
  
  return {
    averageTime,
    maxTime,
    minTime,
    iterations,
  };
}

/**
 * Test 6: Multi-agent communication
 */
async function testAgentCommunication(): Promise<any> {
  const result = await executeTokenSentinel({
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    priority: 'HIGH',
    skipSocialAnalysis: false, // Test full agent chain
  });
  
  // Verify agents were executed in correct order
  const expectedAgents = ['token_hunter', 'contract_auditor', 'onchain_analyst'];
  
  for (const agent of expectedAgents) {
    if (!result.agentsExecuted.includes(agent)) {
      throw new Error(`Agent ${agent} was not executed`);
    }
  }
  
  // Verify data flow between agents
  if (!result.tokenHunterResult || !result.onChainAnalystResult) {
    throw new Error('Agent data not properly passed between agents');
  }
  
  return {
    agentsExecuted: result.agentsExecuted,
    dataFlowValid: true,
  };
}

/**
 * Test 7: External API integration
 */
async function testAPIIntegration(): Promise<any> {
  const result = await executeTokenSentinel({
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    priority: 'MEDIUM',
    skipSocialAnalysis: true,
  });
  
  // Check if external APIs provided data
  const apiResults = {
    birdeye: !!result.tokenHunterResult,
    etherscan: !!result.contractAuditorResult,
    dune: !!result.onChainAnalystResult,
  };
  
  const workingAPIs = Object.values(apiResults).filter(Boolean).length;
  
  if (workingAPIs === 0) {
    throw new Error('No external APIs are working');
  }
  
  return {
    apiResults,
    workingAPIs,
    totalAPIs: Object.keys(apiResults).length,
  };
}

/**
 * Test 8: Risk calculation accuracy
 */
async function testRiskCalculation(): Promise<any> {
  // Test with known safe token
  const safeResult = await executeTokenSentinel({
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    priority: 'HIGH',
    skipSocialAnalysis: true,
  });
  
  // Safe token should have low risk
  if (safeResult.overallRiskScore > 5) {
    throw new Error(`Safe token has high risk score: ${safeResult.overallRiskScore}`);
  }
  
  // Test risk score consistency
  const secondResult = await executeTokenSentinel({
    tokenAddress: TEST_TOKENS.SAFE_TOKEN,
    priority: 'HIGH',
    skipSocialAnalysis: true,
  });
  
  const scoreDifference = Math.abs(safeResult.overallRiskScore - secondResult.overallRiskScore);
  
  if (scoreDifference > 2) {
    throw new Error(`Risk score inconsistent: ${scoreDifference} difference`);
  }
  
  return {
    safeTokenRisk: safeResult.overallRiskScore,
    consistency: scoreDifference < 2,
    scoreDifference,
  };
}

/**
 * Generate test report
 */
export function generateTestReport(testResults: {
  results: TestResult[];
  summary: any;
}): string {
  let report = '# TokenSentinel Integration Test Report\n\n';
  
  // Summary
  report += '## Test Summary\n\n';
  report += `- **Total Tests:** ${testResults.summary.total}\n`;
  report += `- **Passed:** ${testResults.summary.passed}\n`;
  report += `- **Failed:** ${testResults.summary.failed}\n`;
  report += `- **Success Rate:** ${((testResults.summary.passed / testResults.summary.total) * 100).toFixed(1)}%\n`;
  report += `- **Total Duration:** ${testResults.summary.duration}ms\n\n`;
  
  // Detailed results
  report += '## Detailed Results\n\n';
  
  for (const result of testResults.results) {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    report += `### ${result.testName} - ${status}\n\n`;
    report += `- **Duration:** ${result.duration}ms\n`;
    
    if (result.error) {
      report += `- **Error:** ${result.error}\n`;
    }
    
    if (result.details) {
      report += `- **Details:** ${JSON.stringify(result.details, null, 2)}\n`;
    }
    
    report += '\n';
  }
  
  report += '---\n';
  report += `Report generated on ${new Date().toISOString()}\n`;
  
  return report;
}
