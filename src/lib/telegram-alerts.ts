/**
 * @fileOverview Telegram Alert System - Sends real-time rug pull alerts and token analysis
 * Integrates with TokenSentinel workflow to provide instant notifications
 */

import { z } from 'zod';
import type { TokenSentinelOutput } from '../ai/workflows/token-sentinel';

// Telegram Bot configuration
const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;
const TELEGRAM_CHAT_ID = process.env.TELEGRAM_CHAT_ID;
const TELEGRAM_API_BASE = 'https://api.telegram.org/bot';

// Alert configuration schema
export const AlertConfig = z.object({
  minRiskScore: z.number().min(0).max(10).default(6),
  alertLevels: z.array(z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])).default(['HIGH', 'CRITICAL']),
  enablePriceAlerts: z.boolean().default(true),
  enableOpportunityAlerts: z.boolean().default(false),
  rateLimitMinutes: z.number().min(1).max(60).default(5),
});

export type AlertConfig = z.infer<typeof AlertConfig>;

// Rate limiting for alerts
const alertHistory = new Map<string, number>();
const RATE_LIMIT_MS = 5 * 60 * 1000; // 5 minutes

/**
 * Send Telegram alert for token analysis result
 */
export async function sendTokenAlert(
  result: TokenSentinelOutput,
  config: AlertConfig = {}
): Promise<boolean> {
  try {
    if (!TELEGRAM_BOT_TOKEN || !TELEGRAM_CHAT_ID) {
      console.warn('[TelegramAlerts] Telegram credentials not configured');
      return false;
    }
    
    const alertConfig = { ...AlertConfig.parse({}), ...config };
    
    // Check if alert should be sent based on configuration
    if (!shouldSendAlert(result, alertConfig)) {
      return false;
    }
    
    // Rate limiting check
    if (isRateLimited(result.tokenAddress)) {
      console.log(`[TelegramAlerts] Rate limited for token: ${result.tokenAddress}`);
      return false;
    }
    
    // Format alert message
    const message = formatAlertMessage(result);
    
    // Send to Telegram
    const success = await sendTelegramMessage(message);
    
    if (success) {
      // Update rate limiting
      alertHistory.set(result.tokenAddress, Date.now());
      console.log(`[TelegramAlerts] Alert sent for ${result.tokenName}`);
    }
    
    return success;
    
  } catch (error) {
    console.error('[TelegramAlerts] Error sending alert:', error);
    return false;
  }
}

/**
 * Send batch alerts for multiple tokens
 */
export async function sendBatchAlerts(
  results: TokenSentinelOutput[],
  config: AlertConfig = {}
): Promise<{ sent: number; failed: number }> {
  let sent = 0;
  let failed = 0;
  
  // Group alerts by priority
  const criticalAlerts = results.filter(r => r.alertLevel === 'CRITICAL');
  const highAlerts = results.filter(r => r.alertLevel === 'HIGH');
  const mediumAlerts = results.filter(r => r.alertLevel === 'MEDIUM');
  const lowAlerts = results.filter(r => r.alertLevel === 'LOW');
  
  // Send critical alerts first
  for (const result of criticalAlerts) {
    const success = await sendTokenAlert(result, config);
    if (success) sent++;
    else failed++;
    
    // Small delay between critical alerts
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Send high alerts
  for (const result of highAlerts) {
    const success = await sendTokenAlert(result, config);
    if (success) sent++;
    else failed++;
    
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Send summary for medium/low alerts if there are many
  if (mediumAlerts.length > 5 || lowAlerts.length > 10) {
    const summaryMessage = formatBatchSummary(mediumAlerts, lowAlerts);
    const success = await sendTelegramMessage(summaryMessage);
    if (success) sent++;
    else failed++;
  } else {
    // Send individual alerts for smaller batches
    for (const result of [...mediumAlerts, ...lowAlerts]) {
      const success = await sendTokenAlert(result, config);
      if (success) sent++;
      else failed++;
      
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  return { sent, failed };
}

/**
 * Send daily summary report
 */
export async function sendDailySummary(stats: {
  tokensAnalyzed: number;
  rugPullsDetected: number;
  highRiskTokens: number;
  opportunitiesFound: number;
  averageRiskScore: number;
}): Promise<boolean> {
  const message = `📊 **TokenSentinel Daily Report**

🔍 **Tokens Analyzed:** ${stats.tokensAnalyzed}
🚨 **Rug Pulls Detected:** ${stats.rugPullsDetected}
⚠️ **High Risk Tokens:** ${stats.highRiskTokens}
✅ **Opportunities Found:** ${stats.opportunitiesFound}
📈 **Average Risk Score:** ${stats.averageRiskScore.toFixed(1)}/10

Stay safe and keep monitoring! 🛡️`;

  return await sendTelegramMessage(message);
}

/**
 * Check if alert should be sent based on configuration
 */
function shouldSendAlert(result: TokenSentinelOutput, config: AlertConfig): boolean {
  // Check minimum risk score
  if (result.overallRiskScore < config.minRiskScore) {
    return false;
  }
  
  // Check alert levels
  if (!config.alertLevels.includes(result.alertLevel)) {
    return false;
  }
  
  // Check if it's an opportunity alert and they're disabled
  if (result.alertLevel === 'LOW' && result.priceDirection === 'UP' && !config.enableOpportunityAlerts) {
    return false;
  }
  
  // Check if price alerts are disabled
  if (!config.enablePriceAlerts && result.priceDirection !== 'SIDEWAYS') {
    return false;
  }
  
  return result.shouldAlert;
}

/**
 * Check if token is rate limited
 */
function isRateLimited(tokenAddress: string): boolean {
  const lastAlert = alertHistory.get(tokenAddress);
  if (!lastAlert) return false;
  
  return (Date.now() - lastAlert) < RATE_LIMIT_MS;
}

/**
 * Format alert message for Telegram
 */
function formatAlertMessage(result: TokenSentinelOutput): string {
  const riskEmoji = getRiskEmoji(result.alertLevel);
  const directionEmoji = getDirectionEmoji(result.priceDirection);
  
  let message = `${riskEmoji} **${result.alertLevel} RISK ALERT**\n\n`;
  
  message += `🪙 **Token:** ${result.tokenName} (${result.tokenSymbol})\n`;
  message += `📍 **Address:** \`${result.tokenAddress}\`\n`;
  message += `⚡ **Risk Score:** ${result.overallRiskScore.toFixed(1)}/10\n`;
  message += `🎯 **Confidence:** ${(result.confidence * 100).toFixed(0)}%\n`;
  message += `${directionEmoji} **Price Direction:** ${result.priceDirection}\n`;
  message += `🕐 **Analysis Time:** ${result.executionTime}ms\n\n`;
  
  // Add specific risk factors
  if (result.contractAuditorResult) {
    message += `🔍 **Contract Analysis:**\n`;
    if (result.contractAuditorResult.hiddenMintDetected) {
      message += `🔴 Hidden mint function detected\n`;
    }
    if (!result.contractAuditorResult.ownershipRenounced) {
      message += `🟡 Ownership not renounced\n`;
    }
    if (result.contractAuditorResult.blacklistFunction) {
      message += `🔴 Blacklist function present\n`;
    }
    message += `\n`;
  }
  
  if (result.onChainAnalystResult) {
    message += `⛓️ **On-Chain Metrics:**\n`;
    message += `👥 Holder Concentration: ${(result.onChainAnalystResult.holderConcentration * 100).toFixed(1)}%\n`;
    message += `💧 Liquidity: $${result.onChainAnalystResult.liquidityUSD.toLocaleString()}\n`;
    if (result.onChainAnalystResult.suspiciousActivity) {
      message += `🔴 Suspicious activity detected\n`;
    }
    message += `\n`;
  }
  
  if (result.socialSentimentResult) {
    message += `📱 **Social Sentiment:**\n`;
    message += `💬 Mentions: ${result.socialSentimentResult.mentionCount}\n`;
    message += `😊 Sentiment: ${result.socialSentimentResult.sentimentScore.toFixed(2)}\n`;
    if (result.socialSentimentResult.botActivityDetected) {
      message += `🤖 Bot activity detected\n`;
    }
    message += `\n`;
  }
  
  // Add recommended action
  if (result.recommendedAction) {
    message += `💡 **Recommendation:** ${result.recommendedAction}\n\n`;
  }
  
  // Add quick links
  message += `🔗 **Quick Links:**\n`;
  message += `• [DexScreener](https://dexscreener.com/solana/${result.tokenAddress})\n`;
  message += `• [Birdeye](https://birdeye.so/token/${result.tokenAddress})\n`;
  message += `• [Raydium](https://raydium.io/swap/?ammId=${result.tokenAddress})\n`;
  
  // Add timestamp
  message += `\n⏰ ${new Date().toLocaleString()}`;
  
  return message;
}

/**
 * Format batch summary message
 */
function formatBatchSummary(mediumAlerts: TokenSentinelOutput[], lowAlerts: TokenSentinelOutput[]): string {
  let message = `📊 **Token Analysis Summary**\n\n`;
  
  if (mediumAlerts.length > 0) {
    message += `⚠️ **Medium Risk Tokens (${mediumAlerts.length}):**\n`;
    mediumAlerts.slice(0, 10).forEach(token => {
      message += `• ${token.tokenName} - Risk: ${token.overallRiskScore.toFixed(1)}/10\n`;
    });
    if (mediumAlerts.length > 10) {
      message += `... and ${mediumAlerts.length - 10} more\n`;
    }
    message += `\n`;
  }
  
  if (lowAlerts.length > 0) {
    message += `✅ **Low Risk/Opportunities (${lowAlerts.length}):**\n`;
    lowAlerts.slice(0, 5).forEach(token => {
      message += `• ${token.tokenName} - ${token.priceDirection} trend\n`;
    });
    if (lowAlerts.length > 5) {
      message += `... and ${lowAlerts.length - 5} more\n`;
    }
  }
  
  message += `\n⏰ ${new Date().toLocaleString()}`;
  
  return message;
}

/**
 * Send message to Telegram
 */
async function sendTelegramMessage(message: string): Promise<boolean> {
  try {
    const url = `${TELEGRAM_API_BASE}${TELEGRAM_BOT_TOKEN}/sendMessage`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: TELEGRAM_CHAT_ID,
        text: message,
        parse_mode: 'Markdown',
        disable_web_page_preview: true,
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('[TelegramAlerts] API error:', errorData);
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('[TelegramAlerts] Send message error:', error);
    return false;
  }
}

/**
 * Get emoji for risk level
 */
function getRiskEmoji(alertLevel: string): string {
  switch (alertLevel) {
    case 'CRITICAL': return '🚨';
    case 'HIGH': return '⚠️';
    case 'MEDIUM': return '⚡';
    case 'LOW': return '✅';
    default: return '❓';
  }
}

/**
 * Get emoji for price direction
 */
function getDirectionEmoji(direction: string): string {
  switch (direction) {
    case 'UP': return '📈';
    case 'DOWN': return '📉';
    case 'SIDEWAYS': return '➡️';
    default: return '❓';
  }
}

/**
 * Clean up old rate limiting entries
 */
export async function cleanupRateLimit(): Promise<void> {
  const now = Date.now();
  for (const [token, timestamp] of alertHistory.entries()) {
    if (now - timestamp > RATE_LIMIT_MS) {
      alertHistory.delete(token);
    }
  }
}
