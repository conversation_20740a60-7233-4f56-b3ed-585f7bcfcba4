// Singleton pattern to manage WebSocket connections
class WebSocketService {
  private static instances: { [url: string]: WebSocketService } = {};
  private ws: WebSocket | null = null;
  private listeners: ((data: any) => void)[] = [];
  private connectionListeners: ((status: boolean) => void)[] = [];
  private url: string;
  private reconnectInterval: number = 5000; // 5 seconds
  private messageQueue: any[] = [];
  private isConnecting: boolean = false;

  private constructor(url: string) {
    this.url = url;
    this.connect();
  }

  public static getInstance(url: string): WebSocketService {
    if (!WebSocketService.instances[url]) {
      WebSocketService.instances[url] = new WebSocketService(url);
    }
    return WebSocketService.instances[url];
  }

  private connect() {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }
    
    this.isConnecting = true;
    console.log(`[WebSocketService] Connecting to ${this.url}`);
    this.ws = new WebSocket(this.url);

    this.ws.onopen = () => {
      this.isConnecting = false;
      console.log(`[WebSocketService] Connected to ${this.url}`);
      this.notifyConnectionListeners(true);
      this.processMessageQueue();
    };

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.event === 'heartbeat' || data.event === 'systemStatus' || data.event === 'subscriptionStatus') {
          // console.log(`[WebSocketService] Received status message:`, data);
          return;
        }
        this.listeners.forEach(listener => listener(data));
      } catch (error) {
        // console.error('[WebSocketService] Error parsing message:', event.data, error);
      }
    };

    this.ws.onerror = (error) => {
      console.error(`[WebSocketService] Error with ${this.url}:`, error);
      this.isConnecting = false;
    };

    this.ws.onclose = () => {
      console.log(`[WebSocketService] Disconnected from ${this.url}. Reconnecting...`);
      this.notifyConnectionListeners(false);
      this.isConnecting = false;
      this.ws = null;
      setTimeout(() => this.connect(), this.reconnectInterval);
    };
  }
  
  public subscribe(message: any) {
    this.messageQueue.push(message);
    this.processMessageQueue();
  }

  private processMessageQueue() {
    if (this.ws?.readyState !== WebSocket.OPEN) {
        if (!this.isConnecting) {
          this.connect();
        }
        return;
    }

    const processBatch = () => {
        if (this.messageQueue.length === 0) return;

        const message = this.messageQueue.shift();

        // Handle large subscriptions by batching them
        if (message.pair && Array.isArray(message.pair) && message.pair.length > 20) {
            const pairs = message.pair;
            for (let i = 0; i < pairs.length; i += 20) {
                const batch = pairs.slice(i, i + 20);
                const batchMessage = { ...message, pair: batch };
                setTimeout(() => {
                    console.log(`[WebSocketService] Sending subscription batch for ${this.url}`);
                    this.ws?.send(JSON.stringify(batchMessage));
                }, i * 20); // Stagger requests slightly
            }
        } else {
            console.log(`[WebSocketService] Sending message to ${this.url}:`, message);
            this.ws.send(JSON.stringify(message));
        }

        // Process next item in queue
        if (this.messageQueue.length > 0) {
            setTimeout(processBatch, 200); // Small delay between different subscription types
        }
    };
    
    processBatch();
  }

  public addListener(listener: (data: any) => void) {
    this.listeners.push(listener);
  }

  public removeListener(listener: (data: any) => void) {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  public addConnectionListener(listener: (status: boolean) => void) {
    this.connectionListeners.push(listener);
  }

  public removeConnectionListener(listener: (status: boolean) => void) {
    this.connectionListeners = this.connectionListeners.filter(l => l !== listener);
  }

  private notifyConnectionListeners(status: boolean) {
    this.connectionListeners.forEach(l => l(status));
  }
  
  public isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

// Export a singleton instance manager
export const webSocketService = {
  getInstance: (url: string) => WebSocketService.getInstance(url)
};
