#!/usr/bin/env tsx

/**
 * @fileOverview TokenSentinel Integration Test Runner
 * Comprehensive test execution with detailed reporting
 */

import { runIntegrationTests, generateTestReport } from '../src/lib/testing/integration-tests';
import { runValidationSuite, generateValidationReport } from '../src/lib/testing/token-sentinel-validator';
import { healthCheck } from '../src/lib/monitoring/real-time-monitor';

async function main() {
  console.log('🚀 TokenSentinel Integration Test Suite');
  console.log('=====================================\n');

  const startTime = Date.now();

  try {
    // 1. System Health Check
    console.log('📊 1. System Health Check');
    console.log('-------------------------');
    const health = healthCheck();
    console.log(`Status: ${health.status}`);
    console.log(`Monitor Running: ${health.details.monitorRunning}`);
    console.log(`Queue Size: ${health.details.queueSize}`);
    console.log(`Error Rate: ${(health.details.errorRate * 100).toFixed(2)}%\n`);

    // 2. Integration Tests
    console.log('🧪 2. Running Integration Tests');
    console.log('-------------------------------');
    const integrationResults = await runIntegrationTests();
    
    console.log(`\n📋 Integration Test Results:`);
    console.log(`Total Tests: ${integrationResults.summary.total}`);
    console.log(`Passed: ${integrationResults.summary.passed}`);
    console.log(`Failed: ${integrationResults.summary.failed}`);
    console.log(`Success Rate: ${((integrationResults.summary.passed / integrationResults.summary.total) * 100).toFixed(1)}%`);
    console.log(`Duration: ${integrationResults.summary.duration}ms\n`);

    // 3. Validation Suite
    console.log('✅ 3. Running Validation Suite');
    console.log('------------------------------');
    const validationResults = await runValidationSuite();
    
    console.log(`\n📊 Validation Results:`);
    console.log(`Total Tests: ${validationResults.summary.totalTests}`);
    console.log(`Passed: ${validationResults.summary.passed}`);
    console.log(`Failed: ${validationResults.summary.failed}`);
    console.log(`Accuracy: ${(validationResults.summary.accuracy * 100).toFixed(1)}%`);
    console.log(`Avg Execution Time: ${validationResults.summary.averageExecutionTime.toFixed(0)}ms\n`);

    // 4. Generate Reports
    console.log('📄 4. Generating Reports');
    console.log('------------------------');
    
    const integrationReport = generateTestReport(integrationResults);
    const validationReport = generateValidationReport(validationResults.results);
    
    // Save reports
    const fs = await import('fs');
    const path = await import('path');
    
    const reportsDir = path.join(process.cwd(), 'test-reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      path.join(reportsDir, `integration-test-report-${Date.now()}.md`),
      integrationReport
    );
    
    fs.writeFileSync(
      path.join(reportsDir, `validation-report-${Date.now()}.md`),
      validationReport
    );
    
    console.log(`Reports saved to: ${reportsDir}\n`);

    // 5. Final Summary
    const totalDuration = Date.now() - startTime;
    const overallSuccess = integrationResults.summary.failed === 0 && validationResults.summary.failed === 0;
    
    console.log('🎯 5. Final Summary');
    console.log('------------------');
    console.log(`Overall Status: ${overallSuccess ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Total Duration: ${totalDuration}ms`);
    console.log(`Integration Success Rate: ${((integrationResults.summary.passed / integrationResults.summary.total) * 100).toFixed(1)}%`);
    console.log(`Validation Accuracy: ${(validationResults.summary.accuracy * 100).toFixed(1)}%`);
    
    if (overallSuccess) {
      console.log('\n🎉 All tests passed! TokenSentinel is ready for production.');
    } else {
      console.log('\n⚠️  Some tests failed. Review the reports for details.');
    }

    process.exit(overallSuccess ? 0 : 1);

  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
