{"timestamp": "2025-07-15T03:44:51.057Z", "summary": {"total": 6, "passed": 1, "failed": 5, "successRate": 16.666666666666664, "totalDuration": 213}, "results": [{"name": "API Connectivity", "passed": true, "duration": 211, "result": {"apisConfigured": 3, "openRouterStatus": "connected"}}, {"name": "Token Hunter Agent", "passed": false, "duration": 1, "error": "Cannot find module '/Users/<USER>/p/studio/src/ai/agents/token-hunter.js' imported from /Users/<USER>/p/studio/test-tokensentinel.js"}, {"name": "Contract Auditor Agent", "passed": false, "duration": 0, "error": "Cannot find module '/Users/<USER>/p/studio/src/ai/agents/contract-auditor.js' imported from /Users/<USER>/p/studio/test-tokensentinel.js"}, {"name": "Full TokenSentinel Workflow", "passed": false, "duration": 1, "error": "Cannot find module '/Users/<USER>/p/studio/src/ai/workflows/token-sentinel.js' imported from /Users/<USER>/p/studio/test-tokensentinel.js"}, {"name": "UI Integration", "passed": false, "duration": 0, "error": "Cannot find module '/Users/<USER>/p/studio/src/ai/flows/token-sentinel-flow.js' imported from /Users/<USER>/p/studio/test-tokensentinel.js"}, {"name": "Erro<PERSON>", "passed": false, "duration": 0, "error": "Cannot find module '/Users/<USER>/p/studio/src/ai/workflows/token-sentinel.js' imported from /Users/<USER>/p/studio/test-tokensentinel.js"}]}