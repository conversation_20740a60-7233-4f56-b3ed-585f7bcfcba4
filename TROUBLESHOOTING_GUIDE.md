# 🔧 TokenSentinel Troubleshooting Guide

## Quick Diagnostics

### 1. System Health Check
```bash
curl -s http://localhost:9002/api/monitoring/start | jq '.health'
```

### 2. Run Integration Tests
```bash
curl -s http://localhost:9002/api/test | jq '.summary'
```

### 3. Test Token Analysis
```bash
curl -X POST -H "Content-Type: application/json" \
  -d '{"tokenAddress": "So11111111111111111111111111111111111111112"}' \
  http://localhost:9002/api/monitoring/test-token
```

---

## Common Issues & Solutions

### 🚨 Issue: "OpenRouter API error: 401"

**Symptoms:**
- Integration tests fail on API Connectivity
- Error message: "OpenRouter API error: 401"

**Cause:** Invalid or missing OpenRouter API key

**Solution:**
1. Verify your `.env` file contains:
   ```env
   OPENROUTER_API_KEY=sk-or-v1-your-actual-key-here
   ```
2. Check key validity:
   ```bash
   curl -H "Authorization: Bearer $OPENROUTER_API_KEY" \
     https://openrouter.ai/api/v1/models
   ```
3. Get a new key from https://openrouter.ai/ if needed

---

### 🚨 Issue: "Birdeye API key not configured"

**Symptoms:**
- Token Hunter agent fails
- Missing token data in analysis

**Cause:** Missing Birdeye API key

**Solution:**
1. Add to `.env`:
   ```env
   API_BIRDEYE_API_KEY=your-birdeye-key-here
   ```
2. Get key from https://birdeye.so/developers
3. Restart the development server

---

### 🚨 Issue: "Contract source code not available"

**Symptoms:**
- Contract Auditor shows low confidence
- Warning about unverified contracts

**Cause:** Normal behavior for new/unverified tokens

**Solution:**
- This is expected behavior, not an error
- System handles gracefully with reduced confidence
- For testing, use verified contracts like USDC: `EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v`

---

### 🚨 Issue: "Rate limit exceeded"

**Symptoms:**
- API calls failing intermittently
- Slow response times

**Cause:** Hitting API rate limits

**Solution:**
1. Reduce processing frequency:
   ```json
   {
     "processingInterval": 60000,
     "maxTokensPerHour": 20
   }
   ```
2. Enable `skipSocialAnalysis` for faster processing
3. Check API usage on provider dashboards

---

### 🚨 Issue: "Telegram alerts not sending"

**Symptoms:**
- `alertSent: false` in test results
- No Telegram notifications

**Cause:** Missing or incorrect Telegram configuration

**Solution:**
1. Create Telegram bot via @BotFather
2. Add to `.env`:
   ```env
   TELEGRAM_BOT_TOKEN=**********:ABCdefGHIjklMNOpqrsTUVwxyz
   TELEGRAM_CHAT_ID=123456789
   ```
3. Test with:
   ```bash
   curl -X POST -H "Content-Type: application/json" \
     -d '{"type": "alert"}' \
     http://localhost:9002/api/monitoring/telegram-test
   ```

---

### 🚨 Issue: "Maximum agent execution limit reached"

**Symptoms:**
- Warning in agent execution logs
- Analysis completes but with warnings

**Cause:** Safety mechanism to prevent infinite loops

**Solution:**
- This is normal behavior, not an error
- System completes analysis with available data
- Indicates robust error handling is working

---

### 🚨 Issue: "Build errors with 'use server'"

**Symptoms:**
- `npm run build` fails
- Error about server functions

**Cause:** Next.js server action restrictions

**Solution:**
- Already fixed in current implementation
- If you see this, ensure files don't have `'use server'` unless they export only async functions

---

### 🚨 Issue: "Low throughput performance"

**Symptoms:**
- <20 tokens/minute throughput
- Slow response times

**Cause:** API latency or configuration

**Solution:**
1. Enable social analysis skipping:
   ```json
   {"skipSocialAnalysis": true}
   ```
2. Increase batch size:
   ```json
   {"batchSize": 10}
   ```
3. Check network connectivity to APIs

---

### 🚨 Issue: "Memory usage growing over time"

**Symptoms:**
- Increasing memory usage
- Slower performance over time

**Cause:** Potential memory leaks in long-running processes

**Solution:**
1. Restart the monitoring service periodically
2. Monitor with:
   ```bash
   curl -s http://localhost:9002/api/monitoring/start | jq '.stats'
   ```
3. Clear rate limiting cache:
   ```javascript
   // In monitoring configuration
   cleanupInterval: 3600000 // 1 hour
   ```

---

## Performance Optimization

### 1. Speed Optimization
```json
{
  "skipSocialAnalysis": true,
  "batchSize": 10,
  "processingInterval": 15000,
  "priority": "LOW"
}
```

### 2. Accuracy Optimization
```json
{
  "skipSocialAnalysis": false,
  "priority": "HIGH",
  "alertThreshold": 7,
  "minMarketCap": 10000
}
```

### 3. Cost Optimization
```json
{
  "skipSocialAnalysis": true,
  "maxTokensPerHour": 30,
  "priority": "MEDIUM"
}
```

---

## Monitoring & Maintenance

### Daily Health Checks
```bash
# Check system status
curl -s http://localhost:9002/api/monitoring/start | jq '.health.status'

# Verify API connectivity
curl -s http://localhost:9002/api/test | jq '.summary.successRate'

# Monitor performance
curl -X POST -H "Content-Type: application/json" \
  -d '{"testType": "sequential", "tokenCount": 3}' \
  http://localhost:9002/api/performance-test | jq '.summary'
```

### Weekly Maintenance
1. Review error logs
2. Update API keys if needed
3. Check rate limit usage
4. Validate accuracy with known tokens

### Monthly Optimization
1. Analyze performance metrics
2. Update risk thresholds based on results
3. Add new test cases for validation
4. Review and update documentation

---

## Emergency Procedures

### System Down
1. Check development server: `npm run dev`
2. Verify environment variables
3. Test API connectivity
4. Check logs for errors

### High Error Rate
1. Reduce processing frequency
2. Enable social analysis skipping
3. Check API status pages
4. Restart monitoring service

### False Alerts
1. Adjust alert thresholds
2. Review risk calculation weights
3. Add token to whitelist if needed
4. Update validation test cases

---

## Getting Help

### Self-Diagnosis
1. Run integration tests first
2. Check environment variables
3. Verify API key validity
4. Review recent changes

### Log Analysis
- Check browser console for frontend errors
- Review server logs for API errors
- Monitor network requests for failures

### Performance Issues
- Run performance tests
- Check API response times
- Monitor memory usage
- Verify rate limits

---

## Contact & Support

For issues not covered in this guide:
1. Check the main documentation
2. Review the validation report
3. Run diagnostic tests
4. Check API provider status pages

Remember: TokenSentinel is designed to be self-healing and robust. Most issues resolve automatically or through configuration adjustments.
